<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Synchro Systems - AI Powered Reward Management Partner</title>
    <link rel="stylesheet" href="styles.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Geist:wght@100;200;300;400;500;600;700;800;900&family=Outfit:wght@100;200;300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <img
              src="assets/img/3d3352332351ea3daee79707658bdb5d10677277.png"
              alt="Synchro Systems"
            />
          </div>
          <nav class="navigation">
            <a href="#products">Products</a>
            <a href="#company">Company</a>
            <a href="#resources">Resources</a>
            <a href="#contact">Contact</a>
          </nav>
          <div class="header-actions">
            <a href="#" class="login-link">Log In</a>
            <button class="btn btn-outline">Let's Talk</button>
          </div>
          <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>
      <!-- Mobile Menu Overlay -->
      <div class="mobile-menu">
        <nav>
          <a href="#products">Products</a>
          <a href="#company">Company</a>
          <a href="#resources">Resources</a>
          <a href="#contact">Contact</a>
        </nav>
        <div class="mobile-menu-actions">
          <a href="#" class="login-link">Log In</a>
          <button class="btn btn-outline">Let's Talk</button>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background"></div>
      <!-- Floating glass elements -->
      <div class="floating-elements">
        <div class="glass-element glass-1"></div>
        <div class="glass-element glass-2"></div>
        <div class="glass-element glass-3"></div>
        <div class="glass-element glass-4"></div>
        <div class="glass-element glass-5"></div>
        <div class="glass-element glass-6"></div>
        <div class="glass-element glass-7"></div>
      </div>
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              <span class="light">Your AI</span>
              <span class="powered">Powered</span><br />
              <span class="bold">Reward Management</span><br />
              <span class="light">Partner.</span>
            </h1>
            <p class="hero-description">
              Built by Reward Experts. Powering fair play through automation
            </p>
          </div>
          <div class="hero-buttons">
            <div class="hero-btn-wrapper">
              <div class="hero-btn-inner">
                <div class="hero-btn-content">Get Started</div>
              </div>
            </div>
            <div class="hero-btn-wrapper secondary">
              <div class="hero-btn-inner">
                <div class="hero-btn-content">Book a Demo</div>
              </div>
            </div>
          </div>
        </div>
        <div class="hero-image">
          <div class="hero-mockup">
            <div class="mockup-header">
              <div class="mockup-controls">
                <div class="mockup-dots">
                  <div class="dot red"></div>
                  <div class="dot yellow"></div>
                  <div class="dot green"></div>
                </div>
                <div class="mockup-nav-buttons">
                  <div class="nav-button back">‹</div>
                  <div class="nav-button forward">›</div>
                </div>
              </div>
              <div class="mockup-url-bar">
                <div class="url-text">profile.synchrosystems.co.za</div>
              </div>
              <div class="mockup-actions">
                <div class="action-button">⋮</div>
              </div>
            </div>
            <div class="mockup-content">
              <img src="assets/img/dashboard.png" alt="Synchro Platform" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section - Profile Grade Reward -->
    <section class="cta-profile-grade">
      <div class="container">
        <div class="cta-profile-content">
          <div class="cta-decorative-icon">
            <img
              src="assets/img/6b5c65fcd50c625bc586fcd00aad2bf68d050c69.svg"
              alt="Decorative icon"
            />
          </div>
          <div class="cta-profile-text">
            <h2>Profile. Grade. Reward.</h2>
            <p>
              We build innovative SaaS platforms that turn reward analytics into
              fast, data-driven insights, delivering results in half the time.
            </p>
            <button class="btn btn-primary">View products</button>
          </div>
          <div class="cta-decorative-circle">
            <img
              src="assets/img/31fcb7f0c0db1df9a3b4028b67c79b7fff7aac42.png"
              alt="Decorative circle"
            />
          </div>
        </div>
        <div class="cta-background-ellipse">
          <img
            src="assets/img/85e51ea5ffe8029d834e4c165762e0652ed40d7b.png"
            alt="Background ellipse"
          />
        </div>
      </div>
    </section>

    <!-- Products Section -->
    <section class="products" id="products">
      <div class="container">
        <div class="section-header">
          <h2>
            <span class="bold">Our</span> <span class="light">Products.</span>
          </h2>
          <p>
            Where technology meets purpose to deliver faster, smarter, and more
            impactful reward solutions.
          </p>
        </div>
        <div class="products-list">
          <!-- Synchro Profile -->
          <div class="product-card active">
            <div class="product-header">
              <h3>Synchro Profile</h3>
              <button class="expand-btn expanded">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M9 18L15 12L9 6"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
            <div class="product-content">
              <div class="product-text">
                <p><strong>AI enabled job profiling partner</strong></p>
                <p>
                  Leverage artificial intelligence to generate job descriptions
                  quickly, accurately, and at scale. Simply upload an audio
                  transcript, meeting notes, or enter a short prompt — and
                  SynchroProfile will create relevant, structured content
                  aligned to your company's needs.
                </p>
                <p><em>No more starting from scratch.</em></p>
                <p>
                  <strong>Customisable job content to fit your needs</strong>
                </p>
                <p>
                  Refine and tailor your draft job profile by removing or
                  editing specific sections — from key areas of performance,
                  responsibilities, and required skills to qualifications and
                  reporting lines.
                </p>
                <p>
                  Make each profile uniquely yours while ensuring consistency
                  across the organisation.
                </p>
                <button class="btn btn-outline">Get Started Now</button>
              </div>
              <div class="product-image">
                <img
                  src="assets/img/a781d50f6cea2201f7f8e5985284a0c9febd0fa7.png"
                  alt="Synchro Profile"
                />
              </div>
            </div>
          </div>
          <!-- Synchro Grade -->
          <div class="product-card">
            <div class="product-header">
              <h3>Synchro Grade</h3>
              <button class="expand-btn">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M9 18L15 12L9 6"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
          <!-- Synchro Benchmarking -->
          <div class="product-card">
            <div class="product-header">
              <h3>Synchro Benchmarking</h3>
              <button class="expand-btn">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M9 18L15 12L9 6"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section 1 -->
    <!-- <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <svg viewBox="0 0 24 24" fill="none">
                <path
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <span>Why choose Synchro?</span>
            </div>
            <h2>
              <span class="bold">Reward</span>
              <span class="light">confidently.</span>
            </h2>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              nisi ut aliquip ex ea commodo consequat.
            </p>
            <button class="btn btn-primary">Book a Demo</button>
          </div>
          <div class="cta-stats">
            <div class="stat-card large">
              <h3>90%</h3>
              <p>Improving the efficiency of teamwork</p>
              <div class="stat-chart">
                <svg viewBox="0 0 188 98" fill="none">
                  <path
                    d="M0 98V50C20 30 40 20 60 25C80 30 100 40 120 35C140 30 160 20 180 15L188 12V98H0Z"
                    fill="url(#gradient)"
                  />
                  <defs>
                    <linearGradient
                      id="gradient"
                      x1="0%"
                      y1="0%"
                      x2="0%"
                      y2="100%"
                    >
                      <stop
                        offset="0%"
                        style="stop-color: #ea6666; stop-opacity: 0.3"
                      />
                      <stop
                        offset="100%"
                        style="stop-color: #ea6666; stop-opacity: 0"
                      />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
            <div class="stat-cards-small">
              <div class="stat-card">
                <h4>Team Payments</h4>
                <p>Approval 11 March, 2024</p>
                <div class="team-avatars">
                  <img
                    src="http://localhost:3845/assets/86e28d2d7958a43c7dbe9f2371f703f561714e7b.png"
                    alt="Team member"
                  />
                  <img
                    src="http://localhost:3845/assets/23cfbfef76f356f5cdf0462196c44c77ee8a2cea.png"
                    alt="Team member"
                  />
                  <img
                    src="http://localhost:3845/assets/a70388cdef1de611cb817bafd7c5b7c1b8a13955.png"
                    alt="Team member"
                  />
                  <img
                    src="http://localhost:3845/assets/485392b0e19ed71fa791076b6531163db91f0302.png"
                    alt="Team member"
                  />
                  <span class="more-count">10+</span>
                </div>
              </div>
              <div class="stat-card">
                <h4>500+</h4>
                <p>Business already join us!</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section> -->

    <!-- Feature Section -->
    <!-- <section class="feature">
      <div class="container">
        <div class="feature-content">
          <div class="feature-visual">
            <div class="dot-pattern"></div>
            <div class="feature-profiles">
              <img
                src="http://localhost:3845/assets/473daead1baed3f774c1bcf4d42ac32ea6d4498d.png"
                alt="Profile 1"
                class="profile-img profile-1"
              />
              <img
                src="http://localhost:3845/assets/e6b61bf99cee5ef513d5aa8cdda601de96d37717.png"
                alt="Profile 2"
                class="profile-img profile-2"
              />
              <img
                src="http://localhost:3845/assets/32fb4631c3954c4100e4d8c169de94fdc87ffaff.png"
                alt="Profile 3"
                class="profile-img profile-3"
              />
            </div>
            <div class="feature-card">
              <div class="card-header">
                <img
                  src="http://localhost:3845/assets/32fb4631c3954c4100e4d8c169de94fdc87ffaff.png"
                  alt="User"
                  class="user-avatar"
                />
                <div class="user-info">
                  <h4>Kylee Danford</h4>
                  <p><EMAIL></p>
                </div>
              </div>
              <hr />
              <div class="card-details">
                <h5>Customer Details</h5>
                <div class="detail-row">
                  <span>Age</span>
                  <div class="detail-line"></div>
                </div>
                <div class="detail-row">
                  <span>Location</span>
                  <div class="detail-line"></div>
                </div>
                <div class="detail-row">
                  <span>Device</span>
                  <div class="detail-line"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="feature-text">
            <h2>
              <span class="bold">End to end</span>
              <span class="light">workflows.</span>
            </h2>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              enim ad minim veniam, quis nostrud exercitation ullamco laboris
              nisi ut aliquip ex ea commodo consequat.
            </p>
          </div>
        </div>
      </div>
    </section> -->

    <!-- FAQ Section -->
    <section class="faq">
      <div class="container">
        <div class="section-header">
          <h2>
            <span class="bold">Frequently</span>
            <span class="light">Asked Questions.</span>
          </h2>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </div>
        <div class="faq-list">
          <div class="faq-item active">
            <div class="faq-header">
              <span class="faq-number">1</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 14L12 9L17 14"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
            <div class="faq-content">
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
                enim ad minim veniam, quis nostrud exercitation ullamco laboris
                nisi ut aliquip ex ea commodo consequat.
              </p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-header">
              <span class="faq-number">2</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 10L12 15L17 10"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-header">
              <span class="faq-number">3</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 10L12 15L17 10"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-header">
              <span class="faq-number">4</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 10L12 15L17 10"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section 2 -->
    <section class="cta-final">
      <div class="container">
        <div class="cta-card">
          <div class="cta-icons">
            <div class="cta-icon-1"></div>
            <div class="cta-icon-2"></div>
          </div>
          <div class="cta-final-content">
            <h2>
              <span class="bold">Ready</span>
              <span class="light">to get started?</span>
            </h2>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
            <button class="btn btn-primary">Get Started</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2>
              <span class="bold">Get in touch</span>
              <span class="light">for any support and inquiries</span>
            </h2>
            <p>
              The team is happy to answer any questions. Please fill out the
              form and our team will be in touch.
            </p>
          </div>

          <div class="contact-details">
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Address</h4>
                <p>
                  Hertford Office Park, 90 Bekker Road, Midrand, Gauteng, South
                  Africa
                </p>
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Send us a message</h4>
                <a href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Call us</h4>
                <a href="tel:+27104471598">+27 10 447 1598/9</a>
              </div>
            </div>
          </div>

          <div class="contact-form">
            <form>
              <div class="form-row">
                <div class="form-group">
                  <label for="name">Your name</label>
                  <input type="text" id="name" name="name" />
                </div>
                <div class="form-group">
                  <label for="email">Email</label>
                  <input type="email" id="email" name="email" />
                </div>
              </div>
              <div class="form-group">
                <label for="company">Company name</label>
                <input type="text" id="company" name="company" />
              </div>
              <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" rows="5"></textarea>
              </div>
              <button type="submit" class="btn btn-primary">
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-card">
          <div class="footer-content">
            <div class="footer-brand">
              <img
                src="assets/img/3d3352332351ea3daee79707658bdb5d10677277.png"
                alt="Synchro Systems"
              />
              <p>
                Empowering businesses with innovative solutions for digital
                transformation and growth.
              </p>
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
            <div class="footer-links">
              <div class="footer-column">
                <h4>Products</h4>
                <ul>
                  <li><a href="#">Synchro Profile</a></li>
                  <li><a href="#">Synchro Grade</a></li>
                  <li><a href="#">Synchro Reward</a></li>
                  <li><a href="#">Enterprise Solutions</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Company</h4>
                <ul>
                  <li><a href="#">About Us</a></li>
                  <li><a href="#">Our Team</a></li>
                  <li><a href="#">Careers</a></li>
                  <li><a href="#">Contact</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Support</h4>
                <ul>
                  <li><a href="#">Help Center</a></li>
                  <li><a href="#">Documentation</a></li>
                  <li><a href="#">API Reference</a></li>
                  <li><a href="#">Community</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="footer-bottom">
            <p>Copyright © 2025 Synchro Systems. All Rights Reserved.</p>
            <div class="footer-legal">
              <a href="#">Terms & Conditions</a>
              <a href="#">Privacy notice</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script>
      // FAQ Toggle functionality
      document.querySelectorAll(".faq-toggle").forEach((button) => {
        button.addEventListener("click", () => {
          const faqItem = button.closest(".faq-item");
          const isActive = faqItem.classList.contains("active");

          // Close all FAQ items
          document.querySelectorAll(".faq-item").forEach((item) => {
            item.classList.remove("active");
          });

          // Open clicked item if it wasn't active
          if (!isActive) {
            faqItem.classList.add("active");
          }
        });
      });

      // Product expand functionality
      document.querySelectorAll(".expand-btn").forEach((button) => {
        button.addEventListener("click", () => {
          const productCard = button.closest(".product-card");
          const isActive = productCard.classList.contains("active");

          // Close all product cards and reset buttons
          document.querySelectorAll(".product-card").forEach((card) => {
            card.classList.remove("active");
          });
          document.querySelectorAll(".expand-btn").forEach((btn) => {
            btn.classList.remove("expanded");
          });

          // Open clicked card if it wasn't active
          if (!isActive) {
            productCard.classList.add("active");
            button.classList.add("expanded");
          }
        });
      });

      // Mobile menu toggle functionality
      const mobileMenuToggle = document.querySelector(".mobile-menu-toggle");
      const mobileMenu = document.querySelector(".mobile-menu");

      mobileMenuToggle.addEventListener("click", () => {
        mobileMenuToggle.classList.toggle("active");
        mobileMenu.classList.toggle("active");
      });

      // Close mobile menu when clicking on links
      document.querySelectorAll(".mobile-menu a").forEach((link) => {
        link.addEventListener("click", () => {
          mobileMenuToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        });
      });

      // Close mobile menu when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !mobileMenuToggle.contains(e.target) &&
          !mobileMenu.contains(e.target)
        ) {
          mobileMenuToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        }
      });
    </script>
  </body>
</html>
