<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>About Us - Synchro Systems</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900&family=Inter:wght@100..900&family=Outfit:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <img
              src="assets/img/3d3352332351ea3daee79707658bdb5d10677277.png"
              alt="Synchro Systems"
            />
          </div>
          <nav class="navigation">
            <a href="index.html">Products</a>
            <a href="#company">Company</a>
            <a href="#resources">Resources</a>
            <a href="#contact">Contact</a>
          </nav>
          <div class="header-actions">
            <a href="#" class="login-link">Log In</a>
            <button class="btn btn-outline">Let's Talk</button>
          </div>
          <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>
      <!-- Mobile Menu Overlay -->
      <div class="mobile-menu">
        <nav>
          <a href="index.html">Products</a>
          <a href="#company">Company</a>
          <a href="#resources">Resources</a>
          <a href="#contact">Contact</a>
        </nav>
        <div class="mobile-menu-actions">
          <a href="#" class="login-link">Log In</a>
          <button class="btn btn-outline">Let's Talk</button>
        </div>
      </div>
    </header>

    <!-- About Hero Section -->
    <section class="about-hero">
      <div class="container">
        <div class="about-hero-content">
          <div class="about-hero-text">
            <h1>
              <span class="bold">Our</span>
              <span class="light">Story</span>
            </h1>
            <div class="about-hero-description">
              <p class="about-section-title"><span style="color: #2c0846;">About Synchro</span style="color: #2c0846;"></p>
              <p>
                Born from the reward boutique consulting expertise of Sunguti
                Business Solutions, Synchro Systems is more than a digital tool
                — it's a catalyst for so it's your automated reward partner
                built to automate and streamline complex remuneration management
                processes.
              </p>

              <p class="about-section-title"><span style="color: #2c0846;">Our Mission</span style="color: #2c0846;"></p>
              <p>
                To empower organisations with intelligent, data-driven reward
                solutions that promote equity, transparency, and performance —
                transforming how businesses attract, retain, and engage talent.
              </p>

              <p class="about-section-title"><span style="color: #2c0846;">Our Vision</span style="color: #2c0846;"></p>
              <p>
                A future where fair pay and people-centred reward practices
                drive economic inclusion, business sustainability, and social
                impact.
              </p>
            </div>
            <button class="btn btn-primary">See our Leadership</button>
          </div>
          <div class="about-hero-image">
            <img
              src="assets/img/1e6e58a040985939ccc1ae722f34c7e999fe4cb0.png"
              alt="About Synchro"
            />
          </div>
        </div>
        <div class="about-hero-bottom-image">
          <img
            src="assets/img/d63354ffd280ba54b48fcd078ddf1e0ebfc18051.jpg"
            alt="Office Building"
          />
        </div>
      </div>
    </section>

    <!-- Leadership Section -->
    <section class="about-leadership">
      <div class="container">
        <div class="leadership-content">
          <h2>
            <span class="bold">Leadership</span>
            <span class="light">at Synchro</span>
          </h2>
          <p>
            At Synchro, our team brings together deep expertise in reward
            strategy, software engineering, data science and human capital
            consulting. This unique blend of disciplines drives our mission to
            perfect the art and science of pay — delivering smart, scalable
            solutions that are grounded in both technical precision and
            practical application.
          </p>
          <div class="leadership-images">
            <div class="leadership-image">
              <img
                src="assets/img/a4019bd554c979bca8efca36cb54f9655854bba9.png"
                alt="Leadership Team"
              />
            </div>
            <div class="leadership-image">
              <img
                src="assets/img/d02f4c451f7f6dcfd319fe307ab70a2c0b64f916.png"
                alt="Team Collaboration"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="about-team">
      <div class="about-team-background"></div>
      <div class="container">
        <div class="team-header">
          <h2>
            <span class="bold">Meet</span>
            <span class="light">the team</span>
          </h2>
        </div>
        <div class="team-grid">
          <div class="team-card">
            <div class="team-card-image">
              <img
                src="assets/img/b512d4947d0c2c831c1406c5750b7bbd160facae.png"
                alt="Yoli Mqoboli"
              />
            </div>
            <div class="team-card-content">
              <h3>Yoli Mqoboli</h3>
              <p class="team-role">Founder and CEO</p>
              <p class="team-bio">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
              <div class="team-social">
                <img
                  src="assets/img/2c60d2b1c17938afb4c1c357555ede3ba6a6f1bd.svg"
                  alt="Social"
                />
              </div>
            </div>
          </div>
          <div class="team-card">
            <div class="team-card-image">
              <img
                src="assets/img/74edaf0a1a29839917f8b7522dc436fad0d2a332.png"
                alt="Cassius Maluleke"
              />
            </div>
            <div class="team-card-content">
              <h3>Cassius Maluleke</h3>
              <p class="team-role">Head: Development</p>
              <p class="team-bio">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
              <div class="team-social">
                <img
                  src="assets/img/2c60d2b1c17938afb4c1c357555ede3ba6a6f1bd.svg"
                  alt="Social"
                />
              </div>
            </div>
          </div>
          <div class="team-card">
            <div class="team-card-image">
              <img
                src="assets/img/b512d4947d0c2c831c1406c5750b7bbd160facae.png"
                alt="Anelisa Venge"
              />
            </div>
            <div class="team-card-content">
              <h3>Anelisa Venge</h3>
              <p class="team-role">Head: Projects</p>
              <p class="team-bio">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
              <div class="team-social">
                <img
                  src="assets/img/2c60d2b1c17938afb4c1c357555ede3ba6a6f1bd.svg"
                  alt="Social"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-final">
      <div class="container">
        <div class="cta-card">
          <div class="cta-icons">
            <div class="cta-icon-1"></div>
            <div class="cta-icon-2"></div>
          </div>
          <div class="cta-final-content">
            <h2>
              <span class="bold">Ready</span>
              <span class="light">to get started?</span>
            </h2>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
            <button class="btn btn-primary">Request a Demo</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2>
              <span class="bold">Get in touch</span>
              <span class="light">for any support and inquiries</span>
            </h2>
            <p>
              The team is happy to answer any questions. Please fill out the
              form and our team will be in touch.
            </p>
          </div>

          <div class="contact-details">
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Address</h4>
                <p>
                  Hertford Office Park, 90 Bekker Road, Midrand, Gauteng, South
                  Africa
                </p>
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Send us a message</h4>
                <a href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Call us</h4>
                <a href="tel:+27104471598">+27 10 447 1598/9</a>
              </div>
            </div>
          </div>

          <div class="contact-form">
            <form>
              <div class="form-row">
                <div class="form-group">
                  <label for="name">Your name</label>
                  <input type="text" id="name" name="name" />
                </div>
                <div class="form-group">
                  <label for="email">Email</label>
                  <input type="email" id="email" name="email" />
                </div>
              </div>
              <div class="form-group">
                <label for="company">Company name</label>
                <input type="text" id="company" name="company" />
              </div>
              <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" rows="5"></textarea>
              </div>
              <button type="submit" class="btn btn-primary">
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-card">
          <div class="footer-content">
            <div class="footer-brand">
              <img
                src="assets/img/3d3352332351ea3daee79707658bdb5d10677277.png"
                alt="Synchro Systems"
              />
              <p>
                Empowering businesses with innovative solutions for digital
                transformation and growth.
              </p>
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
            <div class="footer-links">
              <div class="footer-column">
                <h4>Products</h4>
                <ul>
                  <li><a href="#">Synchro Profile</a></li>
                  <li><a href="#">Synchro Grade</a></li>
                  <li><a href="#">Synchro Reward</a></li>
                  <li><a href="#">Enterprise Solutions</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Company</h4>
                <ul>
                  <li><a href="#">About Us</a></li>
                  <li><a href="#">Our Team</a></li>
                  <li><a href="#">Careers</a></li>
                  <li><a href="#">Contact</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Support</h4>
                <ul>
                  <li><a href="#">Help Center</a></li>
                  <li><a href="#">Documentation</a></li>
                  <li><a href="#">API Reference</a></li>
                  <li><a href="#">Community</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="footer-bottom">
            <p>Copyright © 2025 Synchro Systems. All Rights Reserved.</p>
            <div class="footer-legal">
              <a href="#">Terms & Conditions</a>
              <a href="#">Privacy notice</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script>
      // Mobile menu toggle functionality
      const mobileMenuToggle = document.querySelector(".mobile-menu-toggle");
      const mobileMenu = document.querySelector(".mobile-menu");

      mobileMenuToggle.addEventListener("click", () => {
        mobileMenuToggle.classList.toggle("active");
        mobileMenu.classList.toggle("active");
      });

      // Close mobile menu when clicking on links
      document.querySelectorAll(".mobile-menu a").forEach((link) => {
        link.addEventListener("click", () => {
          mobileMenuToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        });
      });

      // Close mobile menu when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !mobileMenuToggle.contains(e.target) &&
          !mobileMenu.contains(e.target)
        ) {
          mobileMenuToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        }
      });

      // FAQ Toggle functionality
      document.querySelectorAll(".faq-toggle").forEach((button) => {
        button.addEventListener("click", () => {
          const faqItem = button.closest(".faq-item");
          const isExpanded = faqItem.classList.contains("expanded");

          // Close all FAQ items
          document.querySelectorAll(".faq-item").forEach((item) => {
            item.classList.remove("expanded");
          });

          // If this item wasn't expanded, expand it
          if (!isExpanded) {
            faqItem.classList.add("expanded");
          }
        });
      });
    </script>
  </body>
</html>
