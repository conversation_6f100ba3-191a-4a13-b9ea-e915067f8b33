<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Synchro Profile - Synchro Systems</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900&family=Inter:wght@100..900&family=Outfit:wght@100..900&family=DM+Sans:wght@100..900&family=Figtree:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body class="profile-page">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <img
              src="assets/img/3aae526848728fa071239879c06344cc69338c3c.png"
              alt="Synchro Profile"
            />
          </div>
          <nav class="navigation">
            <a href="synchro-profile.html" class="active">Products</a>
            <a href="#company">Company</a>
            <a href="#resources">Resources</a>
            <a href="#contact">Contact</a>
          </nav>
          <div class="header-actions">
            <a href="#" class="login-link">Log In</a>
            <button class="btn btn-outline">Let's Talk</button>
          </div>
          <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>
      <!-- Mobile Menu Overlay -->
      <div class="mobile-menu">
        <nav>
          <a href="synchro-profile.html" class="active">Products</a>
          <a href="#company">Company</a>
          <a href="#resources">Resources</a>
          <a href="#contact">Contact</a>
        </nav>
        <div class="mobile-menu-actions">
          <a href="#" class="login-link">Log In</a>
          <button class="btn btn-outline">Let's Talk</button>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="profile-hero">
      <div class="profile-hero-background">
        <div class="floating-elements">
          <div class="floating-element floating-element-1"></div>
          <div class="floating-element floating-element-2"></div>
          <div class="floating-element floating-element-3"></div>
          <div class="floating-element floating-element-4"></div>
          <div class="floating-element floating-element-5"></div>
          <div class="floating-element floating-element-6"></div>
          <div class="floating-element floating-element-7"></div>
          <div class="floating-element floating-element-8"></div>
        </div>
      </div>
      <div class="container">
        <div class="profile-hero-content">
          <div class="hero-title-wrapper">
            <h1>
              <span class="bold">Profile with </span>
              <span class="gradient-text">precision.</span>
            </h1>
            <!-- <div class="hero-alarm-icon">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9M15 4V6L21 5.5V4M15 11V13L21 12.5V11M12 7C14.21 7 16 8.79 16 11C16 13.21 14.21 15 12 15C9.79 15 8 13.21 8 11C8 8.79 9.79 7 12 7ZM12 17C12.5 17 13 17.22 13 17.5V20H11V17.5C11 17.22 11.5 17 12 17Z"
                  fill="currentColor"
                />
              </svg>
            </div> -->
          </div>
          <p class="hero-subtitle">Align. Define. Refine.</p>
          <p class="hero-description">
            The right reward decisions start with the right profile.
          </p>
          <div class="hero-buttons">
            <div class="hero-btn-wrapper">
              <div class="hero-btn-inner">
                <div class="hero-btn-content">Get Started</div>
              </div>
            </div>
            <div class="hero-btn-wrapper secondary">
              <div class="hero-btn-inner">
                <div class="hero-btn-content">Book A Free Demo</div>
              </div>
            </div>
          </div>
        </div>
        <div class="profile-mockup">
          <div class="mockup-container">
            <div class="mockup-header">
              <div class="mockup-controls">
                <div class="mockup-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              <div class="mockup-url">profile.synchrosystems.co.za</div>
            </div>
            <div class="mockup-content">
              <img
                src="assets/img/dashboard.png"
                alt="Synchro Profile Interface"
              />
            </div>
            <div class="mockup-fade"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Feature Section 1 -->
    <section class="profile-feature">
      <div class="container">
        <div class="feature-content">
          <div class="feature-text">
            <div class="feature-tag">Clarity & Confidence</div>
            <h2>
              <span class="bold">Create.</span>
              <span class="light">Edit. Approve.</span>
            </h2>
            <div class="feature-description">
              <p>
                SynchroProfile is an AI powered tool that helps you quickly
                create, edit, approve, and maintain job descriptions - making it
                easier for HR teams and consultants to collaborate, standardize
                job structures, and link profiles to job grading and reward
                decisions.
              </p>
              <p>
                Whether you're hiring, benchmarking, or restructuring, Synchro
                Profile ensures that every role starts with clarity and
                confidence.
              </p>
            </div>
            <button class="btn btn-outline">Get Started</button>
          </div>
          <div class="feature-visual">
            <div class="feature-background profile-bg">
              <div class="bg-pattern"></div>
              <div class="bg-lines-horizontal">
                <img
                  src="http://localhost:3845/assets/eb2a4578d1212d3598a25c41bbc7eb07745fe767.svg"
                  alt="Horizontal Lines"
                />
              </div>
              <div class="bg-lines-vertical">
                <img
                  src="http://localhost:3845/assets/d924cf361cd74aac9379849f7ccd9031c0cb39de.svg"
                  alt="Vertical Lines"
                />
              </div>
            </div>
            <div class="feature-cards">
              <div class="feature-card activity-card">
                <div class="card-header">
                  <h3>Activity</h3>
                </div>
                <div class="activity-chart">
                  <div class="chart-container">
                    <div class="chart-grid">
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                      <div class="chart-line"></div>
                    </div>
                    <div class="chart-highlight"></div>
                    <div class="chart-area">
                      <div>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="360"
                          height="159"
                          viewBox="0 0 360 159"
                          fill="none"
                        >
                          <path
                            d="M33.1081 101.92C21.8373 109.464 6.33985 116.158 0 118.561V158.501H359.258V0.961017C338.83 -0.14842 328.968 8.72708 311.357 19.8214C293.746 30.9158 282.123 27.5875 265.217 34.2441C248.311 40.9007 232.813 61.4253 225.769 68.082C218.725 74.7386 207.806 83.0593 187.026 81.9499C166.245 80.8405 125.74 71.965 111.3 70.8556C96.8588 69.7462 84.5314 72.5197 73.6127 76.4028C62.6941 80.2858 47.1967 92.4896 33.1081 101.92Z"
                            fill="url(#paint0_linear_2033_1499)"
                          />
                          <defs>
                            <linearGradient
                              id="paint0_linear_2033_1499"
                              x1="19.4946"
                              y1="0.867195"
                              x2="517.983"
                              y2="177.468"
                              gradientUnits="userSpaceOnUse"
                            >
                              <stop stop-color="#62119D" />
                              <stop offset="1" stop-color="#DEB3FF" />
                            </linearGradient>
                          </defs>
                        </svg>
                      </div>
                    </div>
                    <div class="chart-info">
                      <span class="chart-label">Saved Last Month</span>
                      <span class="chart-value">R50</span>
                    </div>
                  </div>
                </div>
                <div class="chart-dates">
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                  <span>12/9</span>
                </div>
              </div>
              <div class="feature-card profile-card">
                <div class="profile-icon">
                  <div class="icon-bg">
                    <img
                      src="http://localhost:3845/assets/4bc1c893c4e534eb43e3c71afc36f7d7dd5a2940.svg"
                      alt="Icon Background"
                    />
                  </div>
                  <div class="icon-content">
                    <img
                      src="http://localhost:3845/assets/9f6282922341a009739c93b782ca5338517a74bc.svg"
                      alt="Settings Icon"
                    />
                  </div>
                </div>
                <div class="profile-content">
                  <h4>Card Title</h4>
                  <div class="profile-details">
                    <span>Price : R78</span>
                    <div class="divider"></div>
                    <span>Processing : 2 hours</span>
                  </div>
                </div>
              </div>
              <div class="feature-card loading-card">
                <div class="loading-icon">
                  <div class="icon-bg">
                    <img
                      src="http://localhost:3845/assets/25e2df56e65685a897a6ea7ddae575c07b85e673.svg"
                      alt="Icon Background"
                    />
                  </div>
                  <div class="icon-content">
                    <img
                      src="http://localhost:3845/assets/1eddaefe594db2b80896a3a3ce0e669e109ddb93.svg"
                      alt="Cube Icon"
                    />
                  </div>
                </div>
                <div class="loading-content">
                  <div class="loading-bar main-bar"></div>
                  <div class="loading-bars-group">
                    <div class="loading-bar small-bar"></div>
                    <div class="loading-bar small-bar"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Grid Section -->
    <section class="profile-features-grid">
      <div class="profile-features-background">
        <div class="features-gradient-blur"></div>
      </div>
      <div class="container">
        <h2>Features</h2>
        <div class="features-grid">
          <div class="features-column">
            <div class="feature-item">
              <div class="feature-icon">
                <img
                  src="assets/img/features-icon-profile.svg"
                  alt="feature-icon-profile"
                />
              </div>
              <div class="feature-info">
                <h3>AI Powered Job Profiling</h3>
                <p>
                  Leverage AI to generate job profiles quickly and accurately.
                  SynchroProfile analyses role requirements, input documents,
                  and organisational context to produce structured, consistent
                  job profiles — reducing manual effort and increasing quality
                </p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <img
                  src="assets/img/features-icon-profile.svg"
                  alt="feature-icon-profile"
                />
              </div>
              <div class="feature-info">
                <h3>Export to File (Custom Branded)</h3>
                <p>
                  Export finalised job profiles to Word, PDF, or Excel formats.
                  Customise your export templates to match your organisation's
                  corporate identity or client branding guidelines ideal for
                  reporting, sharing, or archiving.
                </p>
              </div>
            </div>
          </div>
          <div class="features-column">
            <div class="feature-item">
              <div class="feature-icon">
                <img
                  src="assets/img/features-icon-profile.svg"
                  alt="feature-icon-profile"
                />
              </div>
              <div class="feature-info">
                <h3>Seamless Collaboration, Review and Approval Workflows</h3>
                <p>
                  Our built in review and approval workflows make it easier for
                  multiple stakeholders - from the incumbent, to line managers
                  and HR — to edit, review, and approve job profiles. This
                  quality steps ensures final job content aligns with your
                  organisation's standards and compliance needs.
                </p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <img
                  src="assets/img/features-icon-profile.svg"
                  alt="feature-icon-profile"
                />
              </div>
              <div class="feature-info">
                <h3>Seamless Integration Across Synchro Products</h3>
                <p>Get notifications for unusual spending activity.</p>
              </div>
            </div>
          </div>
          <div class="features-column">
            <div class="feature-item">
              <div class="feature-icon">
                <img
                  src="assets/img/features-icon-profile.svg"
                  alt="feature-icon-profile"
                />
              </div>
              <div class="feature-info">
                <h3>Version History & Role Governance</h3>
                <p>
                  Track changes, view previous versions, and maintain governance
                  over job data and version control.
                </p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <img
                  src="assets/img/features-icon-profile.svg"
                  alt="feature-icon-profile"
                />
              </div>
              <div class="feature-info">
                <h3>Job Profiling Consulting Support</h3>
                <div class="feature-list">
                  <p>
                    Need extra support? Our experienced job profiling
                    consultants can assist you with:
                  </p>
                  <ol>
                    <li>Crafting or updating job descriptions</li>
                    <li>
                      Facilitating job profiling workshops with your HR teams
                    </li>
                    <li>
                      Reviewing and validating role data for grading and
                      benchmarking
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="features-cta">
          <button class="btn btn-primary profile-page">Book a demo</button>
        </div>
      </div>
    </section>

    <!-- Pricing Philosophy Section -->
    <section class="profile-pricing">
      <div class="container">
        <div class="pricing-content">
          <div class="pricing-visual">
            <div class="pricing-bg"></div>
            <div class="pricing-cards">
              <div class="pricing-card">
                <div class="pricing-icon">
                  <svg viewBox="0 0 24 24" fill="none">
                    <circle
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="2"
                    />
                    <path
                      d="M8 12L10.5 14.5L16 9"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
                <h4>Payment fluid</h4>
                <div class="pricing-chart">
                  <div class="chart-circle">
                    <span>9%</span>
                  </div>
                </div>
              </div>
              <div class="pricing-card">
                <div class="pricing-icon">
                  <svg viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
                <h4>Break fluid</h4>
                <div class="pricing-chart">
                  <div class="chart-circle">
                    <span>25%</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="pricing-graph">
              <h4>Transaction Data</h4>
              <div class="graph-header">
                <span>20 February 2025</span>
                <div class="graph-tabs">
                  <span class="active">Day</span>
                  <span>Week</span>
                  <span>Month</span>
                </div>
              </div>
              <div class="graph-chart">
                <img
                  src="http://localhost:3845/assets/f0502885d85b219d8287a1a13459b0a2b64984da.svg"
                  alt="Transaction Graph"
                />
              </div>
              <div class="graph-times">
                <span>7 am</span>
                <span>9 am</span>
                <span>11 am</span>
                <span>1 pm</span>
                <span>3 pm</span>
                <span>5 pm</span>
                <span>7 pm</span>
                <span>9 pm</span>
              </div>
            </div>
          </div>
          <div class="pricing-text">
            <div class="feature-tag">How we price</div>
            <h2>
              <span class="bold">Pricing</span>
              <span class="light">philosophy</span>
            </h2>
            <div class="pricing-description">
              <p>
                Pay as you go — no annual subscriptions required.
                SynchroProfile's credit-based model, allows you to only pay for
                what you need. Each unique job profile equals one credit.
              </p>
              <p>
                Enjoy a seamless sign-up process with optional onboarding and
                training support.
              </p>
            </div>
            <div class="pricing-features">
              <div class="pricing-feature">
                <div class="check-icon profile-check">
                  <img
                    src="assets/img/d59871b16fd0f3bfb2d0c4af7ffc1fb28116d069.svg"
                    alt="Check"
                  />
                </div>
                <span>Pay As You Go. Top up Anytime.</span>
              </div>
              <div class="pricing-feature">
                <div class="check-icon profile-check">
                  <img
                    src="assets/img/d59871b16fd0f3bfb2d0c4af7ffc1fb28116d069.svg"
                    alt="Check"
                  />
                </div>
                <span>Cost-effective. Scalable. Subscription-free.</span>
              </div>
              <div class="pricing-feature">
                <div class="check-icon profile-check">
                  <img
                    src="assets/img/d59871b16fd0f3bfb2d0c4af7ffc1fb28116d069.svg"
                    alt="Check"
                  />
                </div>
                <span>Volume Discounts</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Plans Section -->
    <section class="profile-plans">
      <div class="container">
        <div class="plans-header">
          <h2>
            <span class="bold">Get </span>
            <span class="light">a Quotation</span>
          </h2>
          <p>
            Please indicate the number of positions that will be profiled.<br />
            This is mandatory to complete as the quote will be based on this
            information and the chosen tier.
          </p>
        </div>
        <div class="plans-grid">
          <div class="plan-card">
            <div class="plan-header">
              <h3>Business</h3>
              <p>Best for companies looking to profile jobs themselves</p>
            </div>
            <div class="plan-divider">
              <img
                src="assets/img/32d18c8ff5f7ca0263bde573fb6d43157b2fde11.svg"
                alt=""
              />
            </div>
            <div class="plan-features">
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>Workflow management</span>
              </div>
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>AI Powered assistance</span>
              </div>
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>Team Management</span>
              </div>
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>Bulk Export</span>
              </div>
            </div>
            <div class="plan-input">
              <label>How many positions do you need to profile?</label>
              <input type="number" placeholder="" />
            </div>
            <button class="btn btn-primary plan-button profile-page">
              Choose
            </button>
          </div>
          <div class="plan-card">
            <div class="plan-header">
              <h3>Professional</h3>
              <p>Best for consultants profiling on behalf of clients</p>
            </div>
            <div class="plan-divider">
              <img
                src="assets/img/32d18c8ff5f7ca0263bde573fb6d43157b2fde11.svg"
                alt=""
              />
            </div>
            <div class="plan-features">
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>Multiple client management</span>
              </div>
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>Workflow Management</span>
              </div>
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>AI Powered assistance</span>
              </div>
              <div class="plan-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>Team Management</span>
              </div>
            </div>
            <div class="plan-input">
              <label>How many positions do you need to profile?</label>
              <input type="number" placeholder="" />
            </div>
            <button class="btn btn-primary plan-button profile-page">
              Choose
            </button>
          </div>
          <div class="plan-card custom-card">
            <div class="plan-header">
              <h3>Custom</h3>
              <p>
                Do you require consultant support with your job profiling
                requirements?
              </p>
            </div>
            <div class="plan-divider">
              <img
                src="assets/img/32d18c8ff5f7ca0263bde573fb6d43157b2fde11.svg"
                alt=""
              />
            </div>
            <div class="plan-features">
              <div class="plan-feature custom-feature">
                <div class="check-icon">
                  <img
                    src="assets/img/7766dbf65f60ea53e9161f6a7653c0aaf5e940c4.svg"
                    alt="Check"
                  />
                </div>
                <span>Please contact Sales for more<br />information</span>
              </div>
            </div>
            <button class="btn btn-primary plan-button profile-page">
              Contact Sales
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
      <div class="container">
        <div class="section-header">
          <h2>
            <span class="bold">Frequently</span>
            <span class="light">Asked Questions</span>
          </h2>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </p>
        </div>
        <div class="faq-list">
          <div class="faq-item active">
            <div class="faq-header">
              <span class="faq-number">1</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 14L12 9L17 14"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
            <div class="faq-content">
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
                enim ad minim veniam, quis nostrud exercitation ullamco laboris
                nisi ut aliquip ex ea commodo consequat.
              </p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-header">
              <span class="faq-number">2</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 10L12 15L17 10"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-header">
              <span class="faq-number">3</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 10L12 15L17 10"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-header">
              <span class="faq-number">4</span>
              <h3>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed?
              </h3>
              <button class="faq-toggle">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M7 10L12 15L17 10"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2>
              <span class="bold">Get in touch</span>
              <span class="light">for any support and inquiries</span>
            </h2>
            <p>
              The team is happy to answer any questions. Please fill out the
              form and our team will be in touch.
            </p>
          </div>

          <div class="contact-details">
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Address</h4>
                <p>
                  Hertford Office Park, 90 Bekker Road, Midrand, Gauteng, South
                  Africa
                </p>
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M3 8L10.89 13.26C11.2187 13.4793 11.6049 13.5963 12 13.5963C12.3951 13.5963 12.7813 13.4793 13.11 13.26L21 8M5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19Z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Send us a message</h4>
                <a href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">
                <svg viewBox="0 0 24 24" fill="none">
                  <path
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4>Call us</h4>
                <a href="tel:+27104471598">+27 10 447 1598/9</a>
              </div>
            </div>
          </div>

          <div class="contact-form">
            <form>
              <div class="form-row">
                <div class="form-group">
                  <label for="name">Your name</label>
                  <input type="text" id="name" name="name" />
                </div>
                <div class="form-group">
                  <label for="email">Email</label>
                  <input type="email" id="email" name="email" />
                </div>
              </div>
              <div class="form-group">
                <label for="company">Company name</label>
                <input type="text" id="company" name="company" />
              </div>
              <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" rows="5"></textarea>
              </div>
              <button type="submit" class="btn btn-primary profile-page">
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-card profile-page footer-profile-page">
          <div class="footer-content">
            <div class="footer-brand">
              <img
                src="assets/img/3d3352332351ea3daee79707658bdb5d10677277.png"
                alt="Synchro Systems"
              />
              <p>
                Empowering businesses with innovative solutions for digital
                transformation and growth.
              </p>
              <a href="mailto:<EMAIL>"
                ><EMAIL></a
              >
            </div>
            <div class="footer-links">
              <div class="footer-column">
                <h4>Products</h4>
                <ul>
                  <li><a href="#">Synchro Profile</a></li>
                  <li><a href="#">Synchro Grade</a></li>
                  <li><a href="#">Synchro Reward</a></li>
                  <li><a href="#">Enterprise Solutions</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Company</h4>
                <ul>
                  <li><a href="#">About Us</a></li>
                  <li><a href="#">Our Team</a></li>
                  <li><a href="#">Careers</a></li>
                  <li><a href="#">Contact</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Support</h4>
                <ul>
                  <li><a href="#">Help Center</a></li>
                  <li><a href="#">Documentation</a></li>
                  <li><a href="#">API Reference</a></li>
                  <li><a href="#">Community</a></li>
                </ul>
              </div>
            </div>
          </div>
          <div class="footer-bottom">
            <p>Copyright © 2025 Synchro Systems. All Rights Reserved.</p>
            <div class="footer-legal">
              <a href="#">Terms & Conditions</a>
              <a href="#">Privacy notice</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script>
      // Mobile menu toggle functionality
      const mobileMenuToggle = document.querySelector(".mobile-menu-toggle");
      const mobileMenu = document.querySelector(".mobile-menu");

      mobileMenuToggle.addEventListener("click", () => {
        mobileMenuToggle.classList.toggle("active");
        mobileMenu.classList.toggle("active");
      });

      // Close mobile menu when clicking on links
      document.querySelectorAll(".mobile-menu a").forEach((link) => {
        link.addEventListener("click", () => {
          mobileMenuToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        });
      });

      // Close mobile menu when clicking outside
      document.addEventListener("click", (e) => {
        if (
          !mobileMenuToggle.contains(e.target) &&
          !mobileMenu.contains(e.target)
        ) {
          mobileMenuToggle.classList.remove("active");
          mobileMenu.classList.remove("active");
        }
      });

      // FAQ Toggle functionality
      document.querySelectorAll(".faq-toggle").forEach((button) => {
        button.addEventListener("click", () => {
          const faqItem = button.closest(".faq-item");
          const isExpanded = faqItem.classList.contains("expanded");

          // Close all FAQ items
          document.querySelectorAll(".faq-item").forEach((item) => {
            item.classList.remove("expanded");
          });

          // If this item wasn't expanded, expand it
          if (!isExpanded) {
            faqItem.classList.add("expanded");
          }
        });
      });
    </script>
  </body>
</html>
