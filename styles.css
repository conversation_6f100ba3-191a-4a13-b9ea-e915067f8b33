/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  line-height: 1.6;
  color: #2c0846;
  background: #ffffff;
}

.container {
  /* max-width: 1440px; */
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.2;
}

.bold {
  font-weight: 800;
}

.light {
  font-weight: 200;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 224px;
  height: 63px;
  border-radius: 10px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(99deg, #62119d 4.7%, #deb3ff 139.86%);
  color: #ffffff;
  box-shadow: 0px 4px 15px rgba(98, 17, 157, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 20px rgba(98, 17, 157, 0.4);
  background: linear-gradient(99deg, #7314b3 4.7%, #e8c4ff 139.86%);
}

.btn-secondary {
  background: #ffffff;
  color: #191715;
  border: 1.5px solid rgba(238, 238, 238, 0.5);
}

.btn-secondary:hover {
  background: #f8f8f9;
}

.btn-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 224px;
  height: 63px;
  background: linear-gradient(#ffffff, #ffffff) padding-box,
    linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%) border-box;
  color: #000000;
  border: 2px solid transparent;
  border-radius: 10px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.7;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
}

.btn-outline::before {
  content: "";
  position: absolute;
  top: 0;
  /* left: -100%; */
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
  border-radius: 10px;
}

.btn-outline:hover::before {
  left: 100%;
}

.btn-outline:hover {
  background: linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0px 6px 20px rgba(106, 68, 255, 0.4);
}
.btn-primary.profile-page {
  background: linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
}

.features-cta .btn-primary.profile-page {
  background: linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%) !important;
}

.btn-primary.profile-page:hover {
  background: linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
}

.features-cta .btn-primary.profile-page:hover {
  background: linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%) !important;
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 25px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo img {
  height: 59px;
  /* width: 335px; */
}

.navigation {
  display: flex;
  gap: 38px;
}

.navigation a {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #373a46;
  text-decoration: none;
  font-weight: 400;
  transition: color 0.3s ease;
}

.navigation a:hover {
  color: #ea6666;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.login-link {
  font-size: 16px;
  color: #191715;
  text-decoration: none;
  font-weight: 400;
}

.login-link:hover {
  color: #ea6666;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.mobile-menu-toggle span {
  width: 100%;
  height: 2px;
  background: #373a46;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu {
  display: none;
  position: fixed;
  top: 109px; /* Height of header + padding */
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  z-index: 999;
  padding: 40px 20px;
  border-top: 1px solid rgba(55, 58, 70, 0.1);
}

.mobile-menu.active {
  display: block;
  animation: slideDown 0.3s ease-out;
}

.mobile-menu nav {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-bottom: 32px;
}

.mobile-menu nav a {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  color: #373a46;
  text-decoration: none;
  font-weight: 400;
  padding: 8px 0;
  border-bottom: 1px solid rgba(55, 58, 70, 0.1);
  transition: color 0.3s ease;
}

.mobile-menu nav a:hover {
  color: #ea6666;
}

.mobile-menu-actions {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  justify-content: center;
}

.mobile-menu-actions a {
  font-size: 16px;
  color: #191715;
  text-decoration: none;
  font-weight: 400;
  text-align: center;
  padding: 12px 16px;
  flex: 1;
}

.mobile-menu-actions .btn {
  flex: 1;
  max-width: none;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  padding-top: 161px;
  /* background: #ffffff; */
  overflow: hidden;
}

.hero::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 50%,
    #ffffff 100%
  );
  z-index: 1;
  pointer-events: none;
}

.hero-background {
  position: absolute;
  top: 672px; /* Exact Figma positioning */
  left: -115px;
  width: 1636px;
  height: 618px;
  z-index: -1;
}

.hero-background::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #ffdaec 0%, #9595ff 100%);
  opacity: 0.4; /* Visible but subtle */
  filter: blur(100px); /* Soft blur effect */
}

/* Floating glass elements - Adjusted for new gradient position */
.floating-elements {
  position: absolute;
  top: 861px; /* Exact Figma positioning */
  left: -68px;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.glass-element {
  position: absolute;
  width: 236px;
  height: 236px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(2px);
}

/* Right side elements */
.glass-1 {
  left: 1085px;
  top: 2px; /* 863px - 861px */
}

.glass-2 {
  left: 1321px;
  top: 131px; /* 992px - 861px */
}

.glass-3 {
  left: 1216px;
  top: 367px; /* 1228px - 861px */
}

/* Left side elements */
.glass-4 {
  left: 61px;
  top: 335px; /* 1196px - 861px */
}

.glass-5 {
  left: 168px;
  top: 0px; /* 861px - 861px */
}

.glass-6 {
  left: -68px;
  top: 0px; /* 861px - 861px */
}

.glass-7 {
  left: 297px;
  top: 236px; /* 1097px - 861px */
  background: rgba(255, 255, 255, 0.1);
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 1040px;
  margin: 0 auto;
  gap: 32px;
}

.hero-title {
  font-size: 80px;
  line-height: 1.1;
  letter-spacing: -4px;
  margin-bottom: 16px;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  color: #2c0846;
}

.hero-title .light {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  color: #2c0846;
}

.hero-title .bold {
  font-family: "Poppins", sans-serif;
  font-weight: 800;
  color: #2c0846;
  letter-spacing: 1px;
}

.hero-title .powered {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  font-style: italic;
  background: linear-gradient(135deg, #ec4899 0%, #d946ef 50%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -4px;
}

.hero-description {
  font-size: 20px;
  color: #5a5a5a;
  opacity: 0.8;
  max-width: 554px;
  margin: 16px 32px;
  text-align: center;
}

.hero-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.hero-btn-wrapper {
  background: rgba(255, 255, 255, 0.25);
  border: 1.5px solid rgba(238, 238, 238, 0.5);
  border-radius: 17px;
  padding: 11px 16px;
  height: 79px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero-btn-inner {
  background: linear-gradient(99deg, #62119d 4.7%, #deb3ff 139.86%);

  border-radius: 10px;
  height: 63px;
  width: 224px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 144px 40px 0px rgba(0, 0, 0, 0),
    0px 92px 37px 0px rgba(0, 0, 0, 0.01), 0px 52px 31px 0px rgba(0, 0, 0, 0.05),
    0px 23px 23px 0px rgba(0, 0, 0, 0.09), 0px 6px 13px 0px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.hero-btn-inner::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
  border-radius: 10px;
  z-index: 1;
}

.hero-btn-wrapper:hover .hero-btn-inner::before {
  left: 100%;
}

.hero-btn-wrapper.secondary .hero-btn-inner {
  background: #ffffff;
}

.hero-btn-content {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.7;
  color: #ffffff;
  white-space: nowrap;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-btn-wrapper.secondary .hero-btn-content {
  color: #191715;
}

.hero-btn-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hero-btn-wrapper:hover .hero-btn-inner {
  box-shadow: 0px 144px 40px 0px rgba(0, 0, 0, 0),
    0px 92px 37px 0px rgba(0, 0, 0, 0.02), 0px 52px 31px 0px rgba(0, 0, 0, 0.08),
    0px 23px 23px 0px rgba(0, 0, 0, 0.12), 0px 6px 13px 0px rgba(0, 0, 0, 0.15);
}

/* Hero Image/Mockup */
.hero-image {
  margin-top: 100px;
  display: flex;
  justify-content: center;
  position: relative;
}

.hero-mockup {
  width: 1123px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 30px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.mockup-header {
  background: url("http://localhost:3845/assets/2911a97f30a3746c140ed9c2f7029ddb2f67c740.png");
  /* background-size: 111.03% 2352.94%; */
  background-position: 49.69% 3.48%;
  background-repeat: no-repeat;
  height: 37px;
  border-radius: 24px 24px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  position: relative;
}

.mockup-controls {
  display: flex;
  align-items: center;
}

.mockup-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.red {
  background: #ff5f57;
}

.dot.yellow {
  background: #ffbd2e;
}

.dot.green {
  background: #28ca42;
}

.mockup-nav-buttons {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.nav-button {
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
}

.mockup-url-bar {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background: #eff1f5;
  border-radius: 6px;
  padding: 2px 12px;
  height: 13px;
  display: flex;
  align-items: center;
  width: 88px;
}

.url-text {
  font-family: "Poppins", sans-serif;
  font-size: 10.325px;
  color: #373a46;
  text-align: center;
  width: 100%;
}

.mockup-actions {
  display: flex;
  align-items: center;
}

.action-button {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
}

.mockup-content {
  background: #ffffff;
  border-radius: 0 0 20px 20px;
  overflow: hidden;
  height: 605px;
  position: relative;
}

.mockup-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.mockup-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 274px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 81.5%
  );
}

/* CTA Profile Grade Reward Section */
.cta-profile-grade {
  padding: 0px 0;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.cta-profile-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  min-height: 409px;
}

.cta-decorative-icon {
  position: absolute;
  left: -300px;
  top: 50%;
  transform: translateY(-50%) rotate(356.875deg);
  width: 15.762px;
  height: 15.762px;
  z-index: 1;
}

.cta-decorative-icon img {
  width: 14.975px;
  height: 14.975px;
}

.cta-profile-text {
  text-align: center;
  max-width: 707px;
  z-index: 3;
}

.cta-profile-text h2 {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-size: 64px;
  line-height: 1.2;
  color: #2c0846;
  letter-spacing: -1.5px;
  margin-bottom: 24px;
}

.cta-profile-text p {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-size: 18px;
  line-height: 1.5;
  color: #5a5a5a;
  margin-bottom: 32px;
  max-width: 521px;
  margin-left: auto;
  margin-right: auto;
}

.cta-decorative-circle {
  position: absolute;
  right: -200px;
  top: 50%;
  transform: translateY(-50%) rotate(337.566deg);
  width: 461.521px;
  height: 333.018px;
  z-index: 1;
}

.cta-decorative-circle img {
  width: 422.6px;
  height: 185.82px;
}

.cta-background-ellipse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 930px;
  height: 288px;
  z-index: 0;
}

.cta-background-ellipse img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Products Section */
.products {
  padding: 120px 0 60px 0;
  /* background: linear-gradient(180deg, #ffffff 0%, #fefbff 100%); */
  background-color: #ffffff;
}

.section-header {
  text-align: left;
  margin-bottom: 60px;
  max-width: 815px;
}

.faq .section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 64px auto;
}

.section-header h2 {
  font-size: 48px;
  margin-bottom: 16px;
  letter-spacing: 1px;
  color: #2c0846;
  font-family: "Poppins", sans-serif;
}

.section-header h2 .bold {
  font-weight: 800;
}

.section-header h2 .light {
  font-weight: 200;
}

.section-header p {
  font-size: 18px;
  color: #5a5a5a;
  line-height: 1.7;
  font-family: "Poppins", sans-serif;
}

.products-list {
  /* max-width: 1296px; */
  margin: 0 auto;
  position: relative;
}

.product-card {
  background: #f8f8f9;
  border-radius: 16px;
  margin-bottom: 8px;
  border: 1px solid rgba(11, 11, 12, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 110px;
}

.product-card.active {
  background: #ffffff;
  border: 3px solid transparent;
  background-image: linear-gradient(#ffffff, #ffffff),
    linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 3;
  height: 100%;
  position: relative;
  box-shadow: 0 20px 40px rgba(234, 102, 102, 0.1);
}

.product-card:not(.active) {
  height: 110px;
  z-index: 1;
}

.product-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px 35px;
}

.product-header h3 {
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 1px;
}

.expand-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.expand-btn.expanded {
  background: linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
  box-shadow: 0 4px 15px rgba(234, 102, 102, 0.3);
  transform: scale(1.05);
}

.expand-btn svg {
  width: 24px;
  height: 24px;
  color: #2c0846;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.expand-btn.expanded svg {
  transform: rotate(90deg);
  color: #ffffff;
}

.expand-btn:hover {
  background: linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
  box-shadow: 0 4px 15px rgba(234, 102, 102, 0.3);
  transform: scale(1.05);
}

.expand-btn:hover svg {
  color: #ffffff;
}

.product-content {
  display: none;
  padding: 0 35px 35px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card.active .product-content {
  display: flex;
  gap: 60px;
  align-items: flex-start;
  margin-top: 20px;
  padding-bottom: 20px;
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.7;
  color: #5a5a5a;
}

.product-text p {
  margin-bottom: 16px;
}

.product-text strong {
  color: #2c0846;
  font-weight: 700;
}

.product-text em {
  font-style: italic;
}

.product-text .btn {
  margin-top: 32px;
  margin-bottom: 20px;
}

.product-image {
  flex-shrink: 0;
  width: 351px;
  height: 363px;
  border-radius: 16px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Feature Section */
.feature {
  padding: 60px 0;
  background: #ffffff;
}

.feature-content {
  display: flex;
  align-items: center;
  gap: 120px;
  /* max-width: 1200px; */
  margin: 0 auto;
}

.feature-visual {
  flex: 1;
  position: relative;
  height: 600px;
}

.dot-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle, #d9d9d9 2px, transparent 2px);
  background-size: 12px 12px;
  opacity: 0.5;
}

.feature-profiles {
  position: relative;
  height: 100%;
}

.profile-img {
  position: absolute;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 3px solid #ffffff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.profile-1 {
  top: 20%;
  left: 20%;
}

.profile-2 {
  top: 15%;
  right: 15%;
}

.profile-3 {
  top: 45%;
  left: 50%;
  transform: translateX(-50%);
  width: 91px;
  height: 91px;
}

.feature-card {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background: #ffffff;
  border-radius: 13px;
  padding: 26px;
  width: 359px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 13px;
  margin-bottom: 26px;
}

.user-avatar {
  width: 53px;
  height: 53px;
  border-radius: 50%;
}

.user-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #0b0b0c;
  margin-bottom: 2px;
}

.user-info p {
  font-size: 15px;
  color: #5d5d77;
}

.card-details h5 {
  font-size: 15px;
  font-weight: 600;
  color: #0b0b0c;
  margin-bottom: 18px;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detail-row span {
  font-size: 15px;
  font-weight: 500;
  color: #383847;
}

.detail-line {
  flex: 1;
  height: 1px;
  background: #e0e0e0;
  margin-left: 20px;
}

.feature-text {
  flex: 1;
}

.feature-text h2 {
  font-size: 48px;
  margin-bottom: 16px;
  letter-spacing: 1px;
}

.feature-text p {
  font-size: 16px;
  color: #757575;
  line-height: 1.7;
}

/* CTA Section */
.cta-section {
  padding: 60px 0;
  background: #ffffff;
  position: relative;
}

.cta-section::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1212px;
  height: 360px;
  background: radial-gradient(
    circle,
    rgba(234, 102, 102, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(40px);
}

.cta-content {
  display: flex;
  align-items: center;
  gap: 80px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  padding: 64px 0;
  border-radius: 24px;
  position: relative;
  z-index: 1;
}

.cta-text {
  flex: 1;
}

.cta-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.cta-badge svg {
  width: 24px;
  height: 24px;
  color: #2c0846;
}

.cta-badge span {
  font-size: 16px;
  color: #2c0846;
}

.cta-text h2 {
  font-size: 48px;
  margin-bottom: 12px;
  letter-spacing: 1px;
}

.cta-text p {
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 40px;
  max-width: 536px;
}

.cta-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-card.large {
  width: 240px;
  margin-bottom: 8px;
}

.stat-card.large h3 {
  font-size: 40px;
  font-weight: 500;
  color: #02140d;
  letter-spacing: -1.5px;
  margin-bottom: 8px;
}

.stat-card.large p {
  font-size: 16px;
  color: #4b5852;
  line-height: 1.7;
  margin-bottom: 50px;
}

.stat-chart {
  height: 98px;
}

.stat-chart svg {
  width: 100%;
  height: 100%;
}

.stat-cards-small {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-cards-small .stat-card {
  width: 240px;
}

.stat-cards-small .stat-card h4 {
  font-size: 18px;
  font-weight: 500;
  color: #02140d;
  margin-bottom: 50px;
}

.stat-cards-small .stat-card p {
  font-size: 12px;
  color: #4b5852;
}

.team-avatars {
  display: flex;
  align-items: center;
  gap: -8px;
  margin-top: 20px;
}

.team-avatars img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  margin-right: -8px;
}

.more-count {
  font-size: 12px;
  color: #02140d;
  margin-left: 8px;
}

/* FAQ Section */
.faq {
  padding: 60px 0;
  /* background: #ffffff; */ /* Commented out to allow gradient visibility */
  position: relative;
}

.faq::before {
  content: "";
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1400px;
  height: 400px;
  background: linear-gradient(90deg, #ffdaec 0%, #9595ff 100%);
  opacity: 0.4;
  filter: blur(100px);
  border-radius: 50%;
  z-index: -1;
}

.faq-list {
  max-width: 768px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.faq-item {
  background: #ffffff;
  border: 1px solid transparent;
  background-image: linear-gradient(#ffffff, #ffffff),
    linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  border-radius: 16px;
  margin-bottom: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item:not(.active) {
  background: #f9f9fa;
  background-image: linear-gradient(#f9f9fa, #f9f9fa),
    linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.faq-header {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 16px;
  cursor: pointer;
}

.faq-number {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(201, 204, 207, 0.4);
  border-radius: 8px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: #5e646a;
  flex-shrink: 0;
}

.faq-item.active .faq-number {
  background: rgba(255, 255, 255, 0.2);
}

.faq-header h3 {
  font-size: 18px;
  font-weight: 400;
  color: #0d2440;
  letter-spacing: 0px;
  flex: 1;
  margin: 0;
  font-family: "Poppins", sans-serif;
  line-height: 1.4;
  width: 648px;
}

.faq-toggle {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.faq-toggle svg {
  width: 24px;
  height: 24px;
  color: #666;
  transition: transform 0.3s ease;
}

.faq-item.active .faq-toggle svg {
  transform: rotate(180deg);
}

.faq-content {
  display: none;
  padding: 16px 24px 24px 96px;
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  letter-spacing: 1px;
  font-family: "Poppins", sans-serif;
  font-weight: 400;
}

.faq-item.active .faq-content {
  display: block;
}

/* CTA Final Section */
.cta-final {
  padding: 60px 0;
  background: #ffffff;
  position: relative;
}

.cta-final .container {
  position: relative;
}

.cta-final .container::before {
  content: "";
  position: absolute;
  top: calc(50% + 60px);
  left: calc(50% - 60px);
  transform: translate(-50%, -50%);
  width: 1150px;
  max-width: calc(100% - 100px);
  height: 520px;
  background: linear-gradient(99deg, #62119d 4.7%, #deb3ff 139.86%);
  border-radius: 30px;
  z-index: 1;
  opacity: 0.66;
  filter: none;
}

.cta-card {
  background: #ffffff;
  border-radius: 30px;
  box-shadow: 0px 3px 29.6px -5px rgba(0, 0, 0, 0.25);
  padding: 60px;
  text-align: center;
  position: relative;
  overflow: hidden;
  /* max-width: 1249px; */
  margin: 0 auto;
  height: 549px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.cta-icons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.cta-icon-1 {
  position: absolute;
  top: 51.55%;
  right: 5.11%;
  width: 11.25%;
  height: 26.59%;
  background-image: url("assets/img/7c4232b4494a5f6e133b4ebe707066d2a770904d.svg");
  background-size: contain;
  background-repeat: no-repeat;
}

.cta-icon-2 {
  position: absolute;
  top: 11.47%;
  left: 7.87%;
  width: 11.64%;
  height: 26.55%;
  background-image: url("assets/img/2f6277c602e34c0ed83e21759e09bfda1bfa6d0b.svg");
  background-size: contain;
  background-repeat: no-repeat;
  transform: rotate(90deg);
}

.cta-final-content {
  position: relative;
  z-index: 1;
}

.cta-final-content h2 {
  font-size: 48px;
  margin-bottom: 16px;
  letter-spacing: 1px;
}

.cta-final-content p {
  font-size: 18px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 40px;
  max-width: 633px;
  margin-left: auto;
  margin-right: auto;
}

/* Contact Section */
.contact {
  padding: 120px 0;
  background: #ffffff;
}

.contact-content {
  display: flex;
  gap: 80px;
  flex-wrap: wrap;
}

.contact-info {
  flex: 1;
  min-width: 300px;
}

.contact-details {
  flex: 1;
  min-width: 300px;
}

.contact-form {
  flex: 1;
  min-width: 400px;
}

/* Desktop: Group contact-info and contact-details together */
@media (min-width: 769px) {
  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-areas:
      "info form"
      "details form";
    gap: 40px 80px;
    row-gap: 4px;
    column-gap: 80px;
  }

  .contact-info {
    grid-area: info;
  }

  .contact-details {
    grid-area: details;
  }

  .contact-form {
    grid-area: form;
  }
}

.contact-info h2 {
  font-size: 48px;
  line-height: 1.2;
  letter-spacing: 1px;
  margin-bottom: 40px;
  max-width: 563px;
}

.contact-info > p {
  font-size: 18px;
  color: #5a5a5a;
  line-height: 1.7;
  max-width: 528px;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 38px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.contact-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 1.5px solid rgba(86, 89, 89, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon svg {
  width: 24px;
  height: 24px;
  color: #2c0846;
}

.contact-item h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c0846;
  margin-bottom: 4px;
}

.contact-item p {
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  letter-spacing: -0.2px;
  max-width: 239px;
}

.contact-item a {
  font-size: 16px;
  color: #757575;
  text-decoration: underline;
  letter-spacing: -0.2px;
}

.contact-item a:hover {
  color: #ea6666;
}

.contact-form {
  flex: 1;
  border-radius: 24px;
  background: linear-gradient(#ffffff, #ffffff) padding-box,
    linear-gradient(313deg, #feede7 8.07%, #f9e9fe 80.05%) border-box;
  background-clip: padding-box, border-box;
  border: 16px solid transparent;
  padding: 40px;
  max-width: 622px;
}

.form-row {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.form-group {
  flex: 1;
  margin-bottom: 32px;
}

.form-row .form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 18px;
  font-weight: 500;
  color: #1d2b19;
  margin-bottom: 12px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 20px;
  border: 1px solid rgba(12, 14, 23, 0.1);
  border-radius: 16px;
  background: #ffffff;
  font-size: 16px;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #ea6666;
}

.form-group textarea {
  resize: vertical;
  min-height: 130px;
}

/* Footer */
.footer {
  padding: 0 0 60px 0;
  background: #ffffff;
  border-top: none;
}

.footer .footer-card {
  background: linear-gradient(
    107deg,
    rgba(126, 87, 155, 0.49) 15.43%,
    rgba(222, 179, 255, 0.49) 51.38%
  );
  border-radius: 30px;
  padding: 80px 60px 60px;
  position: relative;
}

.footer-content {
  display: flex;
  gap: 160px;
  margin-bottom: 80px;
  align-items: flex-start;
}

.footer-brand {
  flex-shrink: 0;
  max-width: 380px;
}

.footer-brand img {
  height: 60px;
  width: auto;
  left: -12px;
  top: -12px;
  position: relative;
}

.footer-brand p {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  color: #484d67;
  line-height: 1.7;
  margin-bottom: 20px;
  font-weight: 400;
}

.footer-brand a {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  color: #030c3b;
  text-decoration: none;
  font-weight: 500;
}

.footer-brand a:hover {
  color: #ea6666;
  text-decoration: underline;
}

.footer-links {
  display: flex;
  gap: 100px;
  flex: 1;
}

.footer-column h4 {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #030c3b;
  margin-bottom: 24px;
  letter-spacing: -0.2px;
}

.footer-column ul {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-column a {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  color: #484d67;
  text-decoration: none;
  transition: color 0.3s ease;
  line-height: 1.6;
  font-weight: 400;
}

.footer-column a:hover {
  color: #ea6666;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.footer-bottom p {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  color: #484d67;
  margin: 0;
  font-weight: 400;
}

.footer-legal {
  display: flex;
  gap: 40px;
}

.footer-legal a {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  color: #484d67;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 400;
}

.footer-legal a:hover {
  color: #ea6666;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    padding: 0 40px;
  }

  .hero-title {
    font-size: 60px;
  }

  .feature-content {
    gap: 60px;
  }

  .cta-content {
    gap: 40px;
  }

  .contact-content {
    gap: 40px;
  }

  .footer-content {
    gap: 80px;
  }
}

/* Tablet breakpoint */
@media (max-width: 1024px) {
  .navigation {
    gap: 24px;
  }

  .navigation a {
    font-size: 15px;
  }

  .header-actions {
    gap: 12px;
  }

  .logo img {
    height: 50px;
    width: auto;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .header-content {
    justify-content: space-between;
  }

  .navigation {
    display: none;
  }

  .header-actions {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .logo img {
    height: 45px;
    width: auto;
  }

  .hero-background::before {
    filter: blur(80px);
  }

  .cta-section::before {
    width: 100vw;
    height: 300px;
    filter: blur(80px);
  }

  .cta-final .container::before {
    width: 100%;
    max-width: 100%;
    height: 400px;
    top: calc(50% + 40px);
    left: calc(50% - 40px);
  }

  .faq::before {
    width: 100vw;
    height: 300px;
    top: 50%;
    filter: blur(80px);
  }

  .hero-title {
    font-size: 40px;
    letter-spacing: -2px;
  }

  .hero-buttons {
    gap: 12px;
    flex-wrap: nowrap;
  }

  .hero-btn-inner {
    width: 180px;
    min-width: 160px;
    font-size: 14px;
  }

  .hero-mockup {
    width: 100%;
  }

  .products {
    padding: 60px 0;
  }

  .feature-content {
    flex-direction: column;
    gap: 40px;
  }

  .feature-visual {
    height: 400px;
  }

  .cta-content {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .contact-content {
    flex-direction: column;
    gap: 40px;
  }

  .contact-info {
    order: 1;
  }

  .contact-form {
    order: 2;
  }

  .contact-details {
    order: 3;
  }

  .contact-info h2 {
    font-size: 40px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .footer-content {
    flex-direction: column;
    gap: 48px;
  }

  .footer-links {
    flex-direction: column;
    gap: 40px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .section-header h2 {
    font-size: 32px;
  }

  .product-content {
    flex-direction: column;
    gap: 30px;
  }

  .product-image {
    width: 100%;
    height: 250px;
  }

  /* CTA Profile Grade responsive */
  .cta-profile-text h2 {
    font-size: 40px;
  }

  .cta-decorative-icon,
  .cta-decorative-circle {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-actions .btn {
    display: none;
  }

  .logo img {
    height: 40px;
    width: auto;
  }

  .hero-background::before {
    filter: blur(60px);
  }

  .cta-section::before {
    width: 100vw;
    height: 200px;
    filter: blur(60px);
  }

  .cta-final .container::before {
    width: 100%;
    height: 300px;
    top: calc(50% + 30px);
    left: calc(50% - 30px);
  }

  .faq::before {
    width: 100vw;
    height: 200px;
    top: 40%;
    filter: blur(60px);
  }

  .hero-buttons {
    gap: 8px;
    flex-wrap: nowrap;
  }

  .hero-btn-inner {
    width: 140px;
    min-width: 120px;
    font-size: 13px;
  }

  .hero-btn-content {
    padding: 0 8px;
  }

  .btn {
    width: 100%;
    max-width: 280px;
  }

  .hero-title {
    font-size: 32px;
  }

  .section-header h2 {
    font-size: 28px;
  }

  .contact-info h2 {
    font-size: 32px;
  }

  .cta-final-content h2 {
    font-size: 32px;
  }

  .cta-icons {
    display: none;
  }

  /* CTA Profile Grade responsive */
  .cta-profile-text h2 {
    font-size: 32px;
  }

  .cta-profile-text p {
    font-size: 18px;
  }
}

/* ===================================
   ABOUT PAGE STYLES
   =================================== */

/* About Hero Section */
.about-hero {
  padding: 158px 0 0;
  background: #ffffff;
  position: relative;
}

.about-hero-content {
  display: flex;
  gap: 48px;
  align-items: flex-start;
  margin-bottom: 120px;
}

.about-hero-text {
  flex: 2;
  max-width: 960px;
}

.about-hero-image {
  flex: 1;
  width: 550px;
  height: 550px;
}

.about-hero-text h1 {
  font-family: "Poppins", sans-serif;
  font-size: 64px;
  font-weight: 800;
  line-height: 1.08;
  color: #2c0846;
  margin-bottom: 48px;
  letter-spacing: 1px;
}

.about-hero-text h1 .bold {
  font-weight: 800;
}

.about-hero-text h1 .light {
  font-weight: 200;
}

.about-hero-description {
  margin-bottom: 48px;
}

.about-hero-description p {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 16px;
}

.about-hero-description p:last-child {
  margin-bottom: 0;
}

.about-section-title {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  color: #5a5a5a;
  margin-bottom: 8px !important;
  margin-top: 16px;
}

.about-section-title:first-child {
  margin-top: 0;
}

.about-hero-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.about-hero-bottom-image {
  width: 100%;
  height: 316px;
  border-radius: 16px;
  overflow: hidden;
}

.about-hero-bottom-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* About Leadership Section */
.about-leadership {
  padding: 94px 0;
  background: #ffffff;
}

.leadership-content h2 {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  font-weight: 800;
  line-height: 1.1;
  color: #2c0846;
  margin-bottom: 40px;
  letter-spacing: 1px;
}

.leadership-content h2 .bold {
  font-weight: 800;
}

.leadership-content h2 .light {
  font-weight: 200;
}

.leadership-content p {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 60px;
  max-width: 1131px;
  margin-left: 161px;
}

.leadership-images {
  display: flex;
  gap: 24px;
}

.leadership-image {
  flex: 1;
  height: 439px;
  border-radius: 8px;
  overflow: hidden;
}

.leadership-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* About Team Section */
.about-team {
  padding: 30px 0 120px 0;
  background: #ffffff;
  position: relative;
}

.about-team-background {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 839px;
  height: 737px;
  pointer-events: none;
  z-index: 1;
  background: linear-gradient(90deg, #ffdaec 0%, #9595ff 100%);
  opacity: 0.4;
  filter: blur(100px);
  border-radius: 50%;
}

.about-team .container {
  position: relative;
  z-index: 2;
}

.team-header {
  margin-bottom: 64px;
}

.team-header h2 {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  font-weight: 800;
  line-height: 1.2;
  color: #2c0846;
  letter-spacing: 1px;
}

.team-header h2 .bold {
  font-weight: 800;
}

.team-header h2 .light {
  font-weight: 200;
}

.team-grid {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.team-card {
  background: linear-gradient(99deg, #62119d 4.7%, #deb3ff 139.86%);
  border-radius: 12px;
  width: 392px;
  overflow: hidden;
}

.team-card-image {
  height: 288px;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.team-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-card-content {
  padding: 24px;
  color: #ffffff;
}

.team-card-content h3 {
  font-family: "Poppins", sans-serif;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.7;
  color: #ffffff;
  margin-bottom: 8px;
}

.team-role {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.7;
  color: #ffffff;
  margin-bottom: 16px;
}

.team-bio {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.7;
  color: #ffffff;
  margin-bottom: 32px;
}

.team-social {
  width: 20px;
  height: 20px;
}

.team-social img {
  width: 100%;
  height: 100%;
}

/* About Contact Section */
.about-contact {
  padding: 191px 0 100px;
  background: #ffffff;
}

.about-contact .contact-content {
  display: flex;
  gap: 80px;
  align-items: flex-start;
}

.about-contact .contact-info {
  flex: 1;
  max-width: 563px;
}

.about-contact .contact-info h2 {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  font-weight: 800;
  line-height: 1.15;
  color: #2c0846;
  margin-bottom: 32px;
  letter-spacing: 1px;
}

.about-contact .contact-info h2 .bold {
  font-weight: 800;
}

.about-contact .contact-info h2 .light {
  font-weight: 200;
}

.about-contact .contact-info > p {
  font-family: "Poppins", sans-serif;
  font-size: 20px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 64px;
  max-width: 528px;
}

.about-contact .contact-details {
  display: flex;
  flex-direction: column;
  gap: 38px;
}

.contact-item {
  display: flex;
  gap: 16px;
  align-items: center;
}

.contact-icon {
  width: 64px;
  height: 64px;
  border: 1.5px solid rgba(86, 89, 89, 0.12);
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon img {
  width: 24px;
  height: 24px;
}

.contact-text h4 {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #2c0846;
  margin-bottom: 4px;
  line-height: 1.7;
}

.contact-text p,
.contact-text a {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  letter-spacing: -0.2px;
  text-decoration: none;
}

.contact-text a {
  color: #5a5a5a;
  text-decoration: underline;
}

.contact-text a:hover {
  color: #ea6666;
}

.about-contact .contact-form {
  background: #ffffff;
  border: 1px solid rgba(12, 14, 23, 0.1);
  border-radius: 24px;
  padding: 33px 39px 39px;
  width: 622px;
  flex-shrink: 0;
}

.form-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.form-group label {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-weight: 500;
  color: #1d2b19;
  line-height: 1.7;
}

.form-group input,
.form-group textarea {
  background: #ffffff;
  border: 1px solid rgba(12, 14, 23, 0.1);
  border-radius: 16px;
  padding: 20px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #1d2b19;
  outline: none;
  transition: border-color 0.3s ease;
}

.form-group input {
  height: 65px;
}

.form-group textarea {
  height: 130px;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #ea6666;
}

.about-contact .contact-form .btn {
  margin-top: 24px;
}

/* About Page Responsive Design */
@media (max-width: 1024px) {
  .about-hero-content {
    flex-direction: column;
    gap: 32px;
  }

  .about-hero-image {
    width: 100%;
    height: 400px;
  }

  .leadership-content p {
    margin-left: 0;
    max-width: 100%;
  }

  .leadership-images {
    flex-direction: column;
    gap: 16px;
  }

  .team-grid {
    flex-direction: column;
    align-items: center;
  }

  .about-contact .contact-content {
    flex-direction: column;
    gap: 60px;
  }

  .about-contact .contact-form {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .about-hero {
    padding: 120px 0 0;
  }

  .about-hero-text h1 {
    font-size: 48px;
  }

  .about-hero-description p {
    font-size: 18px;
  }

  .leadership-content h2,
  .team-header h2,
  .about-contact .contact-info h2 {
    font-size: 36px;
  }

  .leadership-content p,
  .about-contact .contact-info > p {
    font-size: 16px;
  }

  .about-hero-bottom-image {
    height: 200px;
  }

  .leadership-image {
    height: 300px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .about-contact .contact-form {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .about-hero {
    padding: 100px 0 0;
  }

  .about-hero-text h1 {
    font-size: 36px;
  }

  .about-hero-description p {
    font-size: 18px;
  }

  .leadership-content h2,
  .team-header h2,
  .about-contact .contact-info h2 {
    font-size: 28px;
  }

  .leadership-content p,
  .about-contact .contact-info > p {
    font-size: 16px;
  }

  .about-hero-image {
    height: 300px;
  }

  .about-hero-bottom-image {
    height: 150px;
  }

  .leadership-image {
    height: 250px;
  }

  .team-card {
    width: 100%;
    max-width: 350px;
  }

  .about-team-background {
    width: 100%;
    height: 500px;
  }
}

/* ===================================
   SYNCHRO PROFILE PAGE STYLES
   =================================== */

/* Footer-synchro */
.footer-synchro {
  background: linear-gradient(313deg, #feede7 8.07%, #f9e9fe 80.05%);
}
/* Profile Hero Section */
.profile-hero {
  padding: 161px 0 0;
  background: #ffffff;
  position: relative;
  max-height: 1053px;
  overflow: hidden;
}

.profile-hero-background {
  position: absolute;
  top: 672px;
  left: -115px;
  width: 1636px;
  height: 618px;
  z-index: 1;
}

.profile-hero-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #ffdaec 0%, #9595ff 100%);
  opacity: 0.4;
  filter: blur(100px);
  border-radius: 50%;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(2px);
  border-radius: 8px;
}

.floating-element-1 {
  width: 236px;
  height: 236px;
  top: 863px;
  left: 1085px;
}

.floating-element-2 {
  width: 236px;
  height: 236px;
  top: 992px;
  left: 1321px;
}

.floating-element-3 {
  width: 236px;
  height: 236px;
  top: 1228px;
  left: 1216px;
}

.floating-element-4 {
  width: 236px;
  height: 236px;
  top: 1196px;
  left: 61px;
}

.floating-element-5 {
  width: 236px;
  height: 236px;
  top: 861px;
  left: 168px;
}

.floating-element-6 {
  width: 236px;
  height: 236px;
  top: 861px;
  left: -68px;
}

.floating-element-7 {
  width: 236px;
  height: 236px;
  top: 1097px;
  left: 297px;
  background: rgba(255, 255, 255, 0.1);
}

.profile-hero .container {
  position: relative;
  z-index: 2;
}

.profile-hero-content {
  text-align: center;
  margin-bottom: 48px;
}

.hero-title-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

.profile-hero-content h1 {
  font-family: "Poppins", sans-serif;
  font-size: 64px;
  font-weight: 800;
  line-height: 1.1;
  color: #161616;
  margin-bottom: 0;
  letter-spacing: 1px;
  min-width: fit-content;
  display: inline;
}

.profile-hero-content h1 .bold {
  font-weight: 800;
  color: #2c0846;
}

.gradient-text {
  background: linear-gradient(135deg, #ec4899 0%, #d946ef 50%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-alarm-icon {
  position: absolute;
  right: -120px;
  top: 20px;
  width: 68px;
  height: 68px;
  color: #2c0846;
  transform: rotate(14.565deg);
  opacity: 0.8;
}

.hero-alarm-icon svg {
  width: 100%;
  height: 100%;
}

.hero-subtitle {
  font-family: "Poppins", sans-serif;
  font-size: 64px;
  font-weight: 200;
  line-height: 1.1;
  color: #2c0846;
  margin-bottom: 16px;
  letter-spacing: 1px;
  display: block;
}

.hero-description {
  font-family: "Poppins", sans-serif;
  font-size: 20px;
  color: #5a5a5a;
  opacity: 0.8;
  line-height: 1.7;
  margin-bottom: 32px;
  max-width: 554px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  align-items: center;
}

.hero-btn-wrapper {
  background: rgba(255, 255, 255, 0.25);
  border: 1.5px solid rgba(238, 238, 238, 0.5);
  border-radius: 17px;
  padding: 11px 16px;
  height: 79px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero-btn-inner {
  background: linear-gradient(99deg, #62119d 4.7%, #deb3ff 139.86%);
  border-radius: 10px;
  height: 63px;
  width: 224px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 144px 40px 0px rgba(0, 0, 0, 0),
    0px 92px 37px 0px rgba(0, 0, 0, 0.01), 0px 52px 31px 0px rgba(0, 0, 0, 0.05),
    0px 23px 23px 0px rgba(0, 0, 0, 0.09), 0px 6px 13px 0px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.hero-btn-inner::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
  border-radius: 10px;
  z-index: 1;
}

.hero-btn-wrapper:hover .hero-btn-inner::before {
  left: 100%;
}

.hero-btn-wrapper.secondary .hero-btn-inner {
  background: #ffffff;
}

.hero-btn-content {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.7;
  color: #ffffff;
  white-space: nowrap;
  text-align: center;
  position: relative;
  z-index: 2;
}

.hero-btn-wrapper.secondary .hero-btn-content {
  color: #191715;
}

.hero-btn-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hero-btn-wrapper:hover .hero-btn-inner {
  box-shadow: 0px 144px 40px 0px rgba(0, 0, 0, 0),
    0px 92px 37px 0px rgba(0, 0, 0, 0.02), 0px 52px 31px 0px rgba(0, 0, 0, 0.08),
    0px 23px 23px 0px rgba(0, 0, 0, 0.12), 0px 6px 13px 0px rgba(0, 0, 0, 0.15);
}

/* Profile Mockup */
.profile-mockup {
  display: flex;
  justify-content: center;
  margin-top: 96px;
}

.mockup-container {
  width: 1123px;
  position: relative;
}

.mockup-container::before {
  content: "";
  position: absolute;
  top: -54px;
  left: 0;
  right: 0;
  height: 176px;
  background: url("http://localhost:3845/assets/fb758dcff932447634bd1afc9141eb30de790d12.png");
  background-size: 100% 100%;
  transform: scaleY(-1);
  z-index: 1;
}

.mockup-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 761px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  z-index: 2;
}

.mockup-header {
  position: relative;
  z-index: 3;
  background: url("http://localhost:3845/assets/2911a97f30a3746c140ed9c2f7029ddb2f67c740.png");
  /* background-size: 100% 100%; */
  height: 37px;
  border-radius: 24px 24px 0 0;
  margin: 30px 28px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.mockup-controls {
  display: flex;
  align-items: center;
}

.mockup-dots {
  display: flex;
  gap: 4px;
}

.mockup-dots span {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ffffff;
}

.mockup-url {
  font-family: "Poppins", sans-serif;
  font-size: 10.325px;
  color: #373a46;
  background: #eff1f5;
  padding: 2px 8px;
  border-radius: 4px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.mockup-content {
  position: relative;
  z-index: 3;
  margin: 0 28px;
  height: 606px;
  overflow: hidden;
  border-radius: 0 0 20px 20px;
}

.mockup-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.mockup-fade {
  position: absolute;
  bottom: 30px;
  left: 28px;
  right: 28px;
  height: 274px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 81.5%
  );
  z-index: 4;
  border-radius: 0 0 20px 20px;
}

/* Profile Feature Section */
.profile-feature {
  padding: 123px 0 120px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.feature-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.feature-bg::before {
  content: "";
  position: absolute;
  top: 40px;
  left: 0;
  width: 100%;
  height: 368px;
  background: url("http://localhost:3845/assets/e6b363ab865ccd9aba793ba6dff2b409bfa374c4.svg");
  background-size: cover;
  opacity: 0.14;
}

.feature-bg::after {
  content: "";
  position: absolute;
  top: 0;
  left: 40px;
  width: 420px;
  height: 100%;
  background: url("http://localhost:3845/assets/778aa7b907e3241ab85f312942e3742f7fe3d3a2.svg");
  background-size: cover;
  opacity: 0.14;
}

.feature-content {
  display: flex;
  gap: 120px;
  align-items: flex-start;
  position: relative;
  z-index: 2;
}

.feature-text {
  flex: 1;
  max-width: 514px;
}

.feature-tag {
  background: #f9f4fc;
  color: #2c0846;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 600;
  padding: 8px 15px;
  border-radius: 100px;
  display: inline-block;
  margin-bottom: 24px;
  line-height: 1.7;
}

.feature-text h2 {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 1.2;
  color: #2c0846;
  margin-bottom: 16px;
  letter-spacing: 1px;
}

.feature-text h2 .bold {
  font-weight: 800;
}

.feature-text h2 .light {
  font-weight: 200;
}

.feature-description {
  margin-bottom: 40px;
}

.feature-description p {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 16px;
}

.feature-description p:last-child {
  margin-bottom: 0;
}

.feature-visual {
  flex: 1;
  position: relative;
  width: 557px;
  height: 581px;
}

.feature-background {
  position: absolute;
  top: 60px;
  left: 0;
  width: 500px;
  height: 438px;
  z-index: 1;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 500px;
  height: 438px;
  background: #efdaff;
  opacity: 0.105;
  border-radius: 20px;
}

.bg-lines-horizontal {
  position: absolute;
  top: 39.73px;
  left: 0;
  width: 500px;
  height: 368px;
  background: url("http://localhost:3845/assets/e6b363ab865ccd9aba793ba6dff2b409bfa374c4.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  opacity: 0.14;
}

.bg-lines-vertical {
  position: absolute;
  top: 0;
  left: 40.44px;
  width: 415.156px;
  height: 420px;
  background: url("http://localhost:3845/assets/778aa7b907e3241ab85f312942e3742f7fe3d3a2.svg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  opacity: 0.14;
  transform: rotate(90deg);
  transform-origin: center;
}

.feature-cards {
  position: absolute;
  top: 0;
  left: 57px;
  width: 400px;
  height: 458px;
  z-index: 2;
}

.feature-card {
  position: absolute;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.05);
}

.activity-card {
  top: 0;
  width: 400px;
  padding: 20px;
}

.profile-card {
  top: 213px;
  width: 400px;
  height: 90px;
  padding: 18.099px 20.5px;
  display: inline-grid;
  grid-template-columns: max-content;
  grid-template-rows: max-content;
  place-items: start;
}

.loading-card {
  top: 323px;
  width: 400px;
  height: 90px;
  padding: 20px;
  display: inline-grid;
  grid-template-columns: max-content;
  grid-template-rows: max-content;
  place-items: start;
}

.card-header h3 {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #242731;
  margin: 0 0 20px 0;
  line-height: 1.7;
}

.activity-chart {
  position: relative;
  height: 153px;
  margin-bottom: 16px;
}

.chart-container {
  position: relative;
  width: 358px;
  height: 153px;
}

.chart-grid {
  position: absolute;
  top: 17.31px;
  left: 0;
  width: 359.258px;
  height: 185.533px;
  display: flex;
  justify-content: space-between;
}

.chart-line {
  width: 0.309px;
  height: 100%;
  background: #f3f0fe;
}

.chart-highlight {
  position: absolute;
  top: -40.43px;
  left: 158.91px;
  width: 25.352px;
  height: 145px;
  border-radius: 6.183px;
  background: #f7f7f7;
}

.chart-area {
  position: absolute;
  top: 17.93px;
  left: 0;
  width: 359.258px;
  height: 87.805px;
  z-index: 1;
}

.chart-svg {
  position: absolute;
  top: -79.53%;
  left: -0.03%;
  width: 100%;
  height: auto;
  max-width: none;
  z-index: 1;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 100%;
  padding-top: 20px;
}

.chart-bar {
  width: 2px;
  background: #f3f0fe;
  height: 100%;
}

.chart-info {
  position: absolute;
  top: -40px;
  left: 195.79px;
  display: flex;
  flex-direction: column;
  gap: 3.71px;
}

.chart-label {
  font-family: "Poppins", sans-serif;
  font-size: 8.038px;
  font-weight: 500;
  color: #5a5a5a;
  line-height: normal;
  white-space: nowrap;
}

.chart-value {
  font-family: "Poppins", sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #081717;
  line-height: normal;
  white-space: nowrap;
}

.chart-dates {
  position: relative;
  display: inline-grid;
  grid-template-columns: max-content;
  grid-template-rows: max-content;
  place-items: start;
  font-family: "Poppins", sans-serif;
  font-size: 7.42px;
  font-weight: 500;
  color: #ffffff;
  line-height: normal;
  margin-top: 8px;
}

.chart-dates span:nth-child(1) {
  margin-left: 0px;
}
.chart-dates span:nth-child(2) {
  margin-left: 38.002px;
}
.chart-dates span:nth-child(3) {
  margin-left: 76.004px;
}
.chart-dates span:nth-child(4) {
  margin-left: 114.007px;
}
.chart-dates span:nth-child(5) {
  margin-left: 152.008px;
}
.chart-dates span:nth-child(6) {
  margin-left: 190.01px;
}
.chart-dates span:nth-child(7) {
  margin-left: 228.013px;
}
.chart-dates span:nth-child(8) {
  margin-left: 266.015px;
}
.chart-dates span:nth-child(9) {
  margin-left: 304.017px;
}
.chart-dates span:nth-child(10) {
  margin-left: 342.019px;
}

.chart-dates span {
  grid-area: 1 / 1;
  width: 14.97px;
  height: 9.966px;
  font-variation-settings: "opsz" 14;
  white-space: nowrap;
}

/* Profile Card Styling */
.profile-icon {
  grid-area: 1 / 1;
  margin-left: 0;
  margin-top: 2.901px;
  position: relative;
  width: 48px;
  height: 48px;
}

.icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 48px;
  height: 48px;
}

.icon-bg img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.icon-content {
  position: absolute;
  top: 11px;
  left: 11px;
  width: 26px;
  height: 26px;
  overflow: hidden;
}

.icon-content img {
  position: absolute;
  top: -3.85%;
  left: -3.85%;
  width: 107.7%;
  height: 107.7%;
  object-fit: contain;
}

.profile-content {
  grid-area: 1 / 1;
  margin-left: 68.5px;
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.profile-content h4 {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #5a5a5a;
  margin: 0;
  line-height: 1.7;
  white-space: nowrap;
}

.profile-details {
  display: flex;
  align-items: center;
  gap: 17px;
  font-family: "Poppins", sans-serif;
  font-size: 12px;
  color: #526061;
}

.profile-details span:first-child {
  font-weight: 500;
  line-height: 1.5;
}

.profile-details span:last-child {
  font-weight: 400;
  line-height: 1.5;
}

.divider {
  width: 1px;
  height: 17px;
  background: #ececec;
}

/* Loading Card Styling */
.loading-icon {
  grid-area: 1 / 1;
  margin-left: 0;
  margin-top: 0.5px;
  position: relative;
  width: 48px;
  height: 48px;
}

.loading-content {
  grid-area: 1 / 1;
  margin-left: 68.5px;
  margin-top: 11.5px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.main-bar {
  width: 213px;
  height: 9px;
  background: #081717;
  opacity: 0.1;
  border-radius: 20px;
}

.loading-bars-group {
  display: flex;
  gap: 20px;
}

.small-bar {
  width: 65.267px;
  height: 8px;
  border-radius: 20px;
}

.small-bar:first-child {
  background: #e6e8e8;
  opacity: 0.4;
}

.small-bar:last-child {
  background: #eff5f5;
  opacity: 0.3;
}

.profile-card {
  width: 400px;
  height: 90px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.profile-icon {
  width: 48px;
  height: 48px;
  background: #f0ebff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-icon svg {
  width: 26px;
  height: 26px;
  color: #62119d;
}

.profile-card h4 {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #5a5a5a;
  margin-bottom: 6px;
  line-height: 1.7;
}

.profile-details {
  display: flex;
  gap: 17px;
  align-items: center;
}

.profile-details span {
  font-family: "Poppins", sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #526061;
  line-height: 1.5;
}

.profile-details span:first-child::after {
  content: "";
  width: 1px;
  height: 17px;
  background: #ececec;
  margin-left: 17px;
}

.loading-card {
  width: 400px;
  height: 90px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.loading-icon {
  width: 48px;
  height: 48px;
  background: #e6f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon svg {
  width: 26px;
  height: 26px;
  color: #1890ff;
}

.loading-bars {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.loading-bar {
  height: 9px;
  border-radius: 20px;
  background: #081717;
  opacity: 0.1;
}

.loading-bar:first-child {
  width: 213px;
}

.loading-bar:last-child {
  width: 65px;
}

/* Profile Features Grid Section */
.profile-features-grid {
  padding: 147px 0;
  background: #ffffff;
  position: relative;
}

.profile-features-background {
  position: absolute;
  top: 300px;
  left: 50%;
  transform: translateX(-50%);
  width: 1141px;
  height: 465px;
  z-index: 1;
}

.features-gradient-blur {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #ffdaec 0%, #9595ff 100%);
  opacity: 0.4;
  filter: blur(100px);
  border-radius: 50%;
}

.profile-features-grid .container {
  position: relative;
  z-index: 2;
}

.profile-features-grid h2 {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  font-weight: 800;
  line-height: 1.2;
  color: #2c0846;
  text-align: center;
  margin-bottom: 60px;
  letter-spacing: 1px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  max-width: 1299px;
  margin: 0 auto 60px auto;
}

.features-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-item {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  gap: 20px;
  align-items: flex-start;
  width: 100%;
  max-width: 417px;
}

.feature-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin-top: 6px;
}

.feature-icon img {
  width: 100%;
  height: 100%;
}

.feature-info h3 {
  font-family: "Poppins", sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #2c0846;
  margin-bottom: 10px;
  line-height: 1.7;
}

.feature-info p {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  margin: 0;
}

.feature-list ol {
  margin-top: 8px;
  padding-left: 20px;
}

.feature-list li {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 4px;
}

.features-cta {
  text-align: center;
}

.features-cta .btn {
  background: linear-gradient(99deg, #62119d 4.7%, #deb3ff 139.86%);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.14);
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 16px;
  padding: 10px 24px;
  height: 63px;
}

/* Profile Pricing Section */
.profile-pricing {
  padding: 89px 0 120px;
  background: #ffffff;
}

.pricing-content {
  display: flex;
  gap: 120px;
  align-items: center;
}

.pricing-visual {
  flex: 1;
  position: relative;
  width: 500px;
  height: 500px;
}

.pricing-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ebe5fc;
  border-radius: 20px;
}

.pricing-bg::before {
  content: "";
  position: absolute;
  top: 40px;
  left: 0;
  width: 100%;
  height: 420px;
  background: url("http://localhost:3845/assets/8271837e6c54f8145367de5e4c2c2d68ea54c50c.svg");
  background-size: cover;
  opacity: 0.1;
}

.pricing-bg::after {
  content: "";
  position: absolute;
  top: 0;
  left: 40px;
  width: 420px;
  height: 100%;
  background: url("http://localhost:3845/assets/f1ab1b7c51e71b0fb8a41730f49140071944acb9.svg");
  background-size: cover;
  opacity: 0.1;
  transform: rotate(90deg);
  transform-origin: center;
}

.pricing-cards {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  align-items: flex-start;
  justify-content: center;
}

.pricing-card {
  background: #ffffff;
  border: 0.8px solid #eff5f5;
  border-radius: 11px;
  padding: 15px 35px;
  width: 186px;
  height: 212px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.pricing-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.pricing-card:first-child .pricing-icon {
  background: #fd72f2;
}

.pricing-card:last-child .pricing-icon {
  background: #c0f0ab;
}

.pricing-icon svg {
  width: 16px;
  height: 16px;
}

.pricing-card h4 {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #081717;
  text-align: center;
  line-height: 1.5;
}

.pricing-chart {
  width: 90px;
  height: 90px;
  position: relative;
}

.chart-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chart-circle span {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #081717;
  line-height: 1.7;
}

.pricing-graph {
  position: absolute;
  top: 276px;
  left: 50%;
  transform: translateX(-50%);
  width: 392px;
  background: #ffffff;
  border-radius: 11px;
  padding: 16px 19px 18px;
  height: 266px;
}

.pricing-graph h4 {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #081717;
  margin-bottom: 12px;
  line-height: 1.5;
}

.graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.graph-header span {
  font-family: "Poppins", sans-serif;
  font-size: 12px;
  color: #5f6165;
  line-height: 1.5;
}

.graph-tabs {
  background: #f5f4f6;
  border-radius: 8px;
  padding: 0;
  display: flex;
}

.graph-tabs span {
  font-family: "Poppins", sans-serif;
  font-size: 8px;
  font-weight: 600;
  padding: 8px 10px;
  border-radius: 19px;
  cursor: pointer;
  line-height: normal;
}

.graph-tabs span.active {
  background: #c5fa59;
  color: #081717;
}

.graph-tabs span:not(.active) {
  color: #526061;
}

.graph-chart {
  height: 167px;
  margin-bottom: 12px;
  position: relative;
}

.graph-chart img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.graph-times {
  display: flex;
  justify-content: space-between;
  font-family: "Poppins", sans-serif;
  font-size: 10px;
  color: #526061;
  line-height: normal;
}

.pricing-text {
  flex: 1;
  max-width: 477px;
}

.pricing-text h2 {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 1.2;
  color: #2c0846;
  margin-bottom: 16px;
  letter-spacing: 1px;
}

.pricing-text h2 .bold {
  font-weight: 800;
}

.pricing-text h2 .light {
  font-weight: 200;
}

.pricing-description {
  margin-bottom: 40px;
}

.pricing-description p {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  margin-bottom: 16px;
}

.pricing-description p:last-child {
  color: #5a5a5a;
  margin-bottom: 0;
}

.pricing-features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pricing-feature {
  display: flex;
  align-items: center;
  gap: 12px;
}

.check-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.check-icon svg {
  width: 12px;
  height: 12px;
  color: #ffffff;
}

.profile-pricing .check-icon.profile-check {
  background: #2c0846;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-pricing .check-icon.profile-check img {
  width: 12px;
  height: 12px;
}

.pricing-feature span {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-weight: 500;
  color: #5a5a5a;
  line-height: 1.7;
}

/* Profile Plans Section */
.profile-plans {
  padding: 98px 0 120px;
  background: #ffffff;
}

.plans-header {
  text-align: center;
  margin-bottom: 80px;
}

.plans-header h2 {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  font-weight: 800;
  line-height: 1.15;
  color: #2c0846;
  margin-bottom: 24px;
  letter-spacing: 1px;
}

.plans-header h2 .bold {
  font-weight: 800;
}

.plans-header h2 .light {
  font-weight: 200;
}

.plans-header p {
  font-family: "Poppins", sans-serif;
  font-size: 20px;
  color: #5a5a5a;
  line-height: 1.7;
  max-width: 802px;
  margin: 0 auto;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(3, 403px);
  gap: 44px;
  max-width: 1299px;
  margin: 0 auto;
  width: 100%;
  justify-content: center;
}

.plan-card {
  background: linear-gradient(#ffffff, #ffffff) padding-box,
    linear-gradient(267deg, #ea6666 2.44%, #6a44ff 95.58%) border-box;
  border: 2px solid transparent;
  border-radius: 16px;
  padding: 31px 34px;
  width: 403px;
  height: 613px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.plan-header {
  margin-bottom: 0px;
}

.plan-header h3 {
  font-family: "Poppins", sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #2c0846;
  text-align: center;
  margin-bottom: 8px;
  letter-spacing: 0.2px;
  line-height: 1.7;
}

.plan-header p {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  color: #5a5a5a;
  line-height: 1.7;
  margin: 0;
}

.plan-divider {
  margin: 0 0 52px 0;
  width: 335px;
  height: 1px;
  align-self: center;
}

.plan-divider img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.plan-features {
  flex: 1;
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.plan-feature {
  display: flex;
  align-items: center;
  gap: 8px;
}

.check-icon {
  width: 22px;
  height: 22px;
  flex-shrink: 0;
  overflow: hidden;
}

.check-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.plan-feature span {
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #5a5a5a;
  line-height: 1.7;
}

.custom-feature span {
  line-height: 1.7;
}

.plan-input {
  margin-bottom: 24px;
}

.plan-input label {
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #1d2b19;
  display: block;
  margin-bottom: 12px;
  line-height: 1.7;
}

.plan-input input {
  width: 100%;
  max-width: 308px;
  height: 44px;
  background: #ffffff;
  border: 1px solid rgba(12, 14, 23, 0.1);
  border-radius: 10px;
  padding: 0 16px;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  color: #5a5a5a;
  outline: none;
  box-sizing: border-box;
}

.plan-input input:focus {
  border-color: #ea6666;
}

.plan-button {
  background: linear-gradient(99deg, #62119d 4.7%, #deb3ff 139.86%);
  color: #ffffff;
  border: none;
  border-radius: 10px;
  padding: 15px 32px;
  font-family: "Poppins", sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1.7;
  width: auto;
  min-width: 120px;
}

.plan-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(98, 17, 157, 0.3);
}

.custom-card .plan-input {
  display: none;
}

.plan-card .btn {
  width: 100%;
  height: auto;
  padding: 15px 32px;
}

/* Synchro Profile Page Responsive Design */
@media (max-width: 1200px) {
  .profile-hero-content h1,
  .hero-subtitle {
    font-size: 64px;
  }

  .feature-content,
  .pricing-content {
    gap: 80px;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .plans-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }

  .plan-card {
    width: 100%;
    max-width: 403px;
  }
}

@media (max-width: 1024px) {
  .profile-hero {
    padding: 140px 0 0;
    min-height: auto;
  }

  .profile-hero-content h1,
  .hero-subtitle {
    font-size: 56px;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .mockup-container {
    width: 90%;
  }

  .feature-content,
  .pricing-content {
    flex-direction: column;
    gap: 60px;
  }

  .plan-card {
    width: 100%;
    max-width: 403px;
  }

  .feature-visual,
  .pricing-visual {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .plan-card {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .profile-hero {
    padding: 120px 0 0;
  }

  .profile-hero-content h1,
  .hero-subtitle {
    font-size: 42px;
  }

  .hero-description {
    font-size: 18px;
  }

  .hero-btn-wrapper {
    width: 100%;
    max-width: 300px;
  }

  .hero-btn-inner {
    width: 100%;
  }

  .feature-text h2,
  .pricing-text h2,
  .profile-features-grid h2,
  .plans-header h2 {
    font-size: 36px;
  }

  .feature-description p,
  .pricing-description p {
    font-size: 14px;
  }

  .feature-cards {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
  }

  .profile-card,
  .loading-card {
    width: 100%;
  }

  .activity-card {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .profile-hero-content h1,
  .hero-subtitle {
    font-size: 32px;
  }

  .hero-description {
    font-size: 16px;
  }

  .hero-btn-wrapper {
    max-width: 250px;
  }

  .feature-text h2,
  .pricing-text h2,
  .profile-features-grid h2,
  .plans-header h2 {
    font-size: 28px;
  }

  .plans-header p {
    font-size: 18px;
  }

  .floating-elements {
    display: none;
  }

  .hero-alarm-icon {
    display: none;
  }
}

.footer-card.profile-page {
  background: linear-gradient(313deg, #feede7 8.07%, #f9e9fe 80.05%) !important;
}

/* ===================================
   SYNCHRO GRADE PAGE STYLES
   =================================== */

/* Grade-specific gradient text - Figma colors */
.profile-hero.grade-page .gradient-text {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700; /* Bold weight for "Confidence" */
}

/* Grade-specific purple text */
.profile-hero.grade-page .purple-text {
  color: #2c0846;
  font-weight: 800;
  font-size: 48px;
}

/* Grade hero title styling */
.profile-hero.grade-page .hero-title {
  font-family: "Poppins", sans-serif;
  font-size: 64px;
  font-weight: 400;
  line-height: 1.1;
  color: #161616;
  margin-bottom: 32px;
  letter-spacing: 1px;
  text-align: center;
}

.profile-hero.grade-page .hero-title .light {
  font-weight: 200; /* Regular weight for "Navigate Your Reward Landscape with" */
}

.profile-hero.grade-page .hero-title .bold {
  font-weight: 800; /* Extra bold for "One Job Evaluation at a time" */
}

/* Grade Hero Buttons - Figma colors */
.profile-hero.grade-page .hero-btn-inner {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  box-shadow: 0px 144px 40px 0px rgba(0, 0, 0, 0),
    0px 92px 37px 0px rgba(0, 0, 0, 0.01), 0px 52px 31px 0px rgba(0, 0, 0, 0.05),
    0px 23px 23px 0px rgba(0, 0, 0, 0.09), 0px 6px 13px 0px rgba(0, 0, 0, 0.1);
}

.profile-hero.grade-page .hero-btn-wrapper.secondary .hero-btn-inner {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0px 144px 40px 0px rgba(0, 0, 0, 0),
    0px 92px 37px 0px rgba(0, 0, 0, 0.01), 0px 52px 31px 0px rgba(0, 0, 0, 0.05),
    0px 23px 23px 0px rgba(0, 0, 0, 0.09), 0px 6px 13px 0px rgba(0, 0, 0, 0.1);
}

.profile-hero.grade-page .hero-btn-wrapper.secondary .hero-btn-content {
  color: #161616;
}

/* Grade Feature Section Buttons */
.profile-feature.grade-page .btn-outline {
  background: linear-gradient(#ffffff, #ffffff) padding-box,
    linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%) border-box;
  border: 2px solid transparent;
  color: #237cbf;
}

.profile-feature.grade-page .btn-outline:hover {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  color: #ffffff;
}

/* Grade Features Grid Buttons */
.profile-features-grid.grade-page .btn-primary {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  border: none;
  color: #ffffff;
}

.profile-features-grid.grade-page .btn-primary:hover {
  background: linear-gradient(267deg, #1a5a8a 2.44%, #2db5b0 95.58%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(35, 124, 191, 0.3);
}

/* Grade Plans Section Buttons */
.profile-plans.grade-page .btn-primary {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  border: none;
  color: #ffffff;
}

.profile-plans.grade-page .btn-primary:hover {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(65, 233, 226, 0.3);
}

.profile-plans.grade-page .plan-button {
  padding: 15px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 10px;
  width: auto;
  min-width: 120px;
}

.profile-plans.grade-page .plan-card {
  background: linear-gradient(#ffffff, #ffffff) padding-box,
    linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%) border-box;
  border: 2px solid transparent;
  height: 700px;
  justify-content: space-between;
}

.profile-plans.grade-page .plan-features {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.profile-plans.grade-page .plan-input {
  margin-top: auto;
  margin-bottom: 16px;
}

.profile-pricing.grade-page .pricing-bg {
  background: #c5f8f6;
}

/* Grade Feature Navigation */
.profile-feature.grade-page .feature-navigation {
  display: flex;
  justify-content: space-between;
  margin-bottom: 95px;
  padding-top: 40px;
}

.profile-feature.grade-page .nav-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  width: 300px;
}

.profile-feature.grade-page .nav-line {
  width: 100%;
  height: 2px;
  background: #e5e5e5;
}

.profile-feature.grade-page .nav-item.active .nav-line {
  background: linear-gradient(90deg, #237cbf 0%, #41e9e2 100%);
}

.profile-feature.grade-page .nav-content {
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 8px;
}

.profile-feature.grade-page .nav-content h3 {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-weight: 500;
  color: #2c0846;
  margin: 0;
  text-align: center;
}

.profile-feature.grade-page .nav-item.active .nav-content h3 {
  color: #2c0846;
}

.profile-feature.grade-page .nav-number {
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: #2481c1;
}

.profile-feature.grade-page .nav-item.active .nav-number {
  color: #2481c1;
}

.profile-feature.grade-page .nav-item:not(.active) .nav-number {
  color: #526061;
}

/* Grade Feature Tag */
.profile-feature.grade-page .feature-tag.grade-why-tag {
  background: rgba(73, 232, 226, 0.32);
  color: #2481c1;
  padding: 8px 15px;
  border-radius: 100px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 16px;
}

/* Grade Feature Quote */
.profile-feature.grade-page .feature-quote {
  font-style: italic;
  font-weight: bold;
  margin-top: 16px;
}

/* Grade Get Started Button */
.profile-feature.grade-page .btn.grade-get-started {
  border: 1.5px solid #237cbf;
  color: #081717;
  background: transparent;
  font-weight: bold;
}

/* Grade Feature Visual Background */
.profile-feature.grade-page .feature-background.grade-bg {
  background: #deeff6;
  opacity: 0.105;
}

.profile-feature.grade-page .bg-lines-horizontal,
.profile-feature.grade-page .bg-lines-vertical {
  opacity: 0.14;
}

.profile-feature.grade-page .bg-lines-horizontal img,
.profile-feature.grade-page .bg-lines-vertical img {
  width: 100%;
  height: 100%;
}

/* Grade FAQ Section - Figma gradient borders */
.faq.grade-page .faq-item {
  background: linear-gradient(#ffffff, #ffffff) padding-box,
    linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%) border-box;
  border: 2px solid transparent;
  border-radius: 16px;
}

.faq.grade-page::before {
  background: linear-gradient(90deg, #e0f7ff 0%, #41e9e2 100%);
}

/* Grade Contact Form - Figma gradient border */
.contact.grade-page .contact-form {
  border-radius: 16px;
  background: linear-gradient(#ffffff, #ffffff) padding-box,
    linear-gradient(
        267deg,
        rgba(35, 124, 191, 0.4) 2.44%,
        rgba(65, 233, 226, 0.26) 95.58%
      )
      border-box;
  border: 16px solid transparent;
  background-clip: padding-box, border-box;
}

.contact.grade-page .contact-form .btn-primary {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  border: none;
  color: #ffffff;
}

.contact.grade-page .contact-form .btn-primary:hover {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(35, 124, 191, 0.3);
}

/* Grade Footer - Figma gradient background */
.footer-card.grade-page {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%) !important;
}

.footer-card.grade-page .footer-content h4,
.footer-card.grade-page .footer-content .footer-description,
.footer-card.grade-page .footer-content a,
.footer-card.grade-page .footer-content p {
  color: #ffffff;
}

.footer-card.grade-page .footer-content a:hover {
  color: #e0f7ff;
}

.footer-card.grade-page .footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

/* Grade Pricing Philosophy */
.profile-pricing.grade-page .feature-tag.grade-tag {
  background: #c5f8f6;
  color: #2481c1;
  padding: 8px 15px;
  border-radius: 100px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-size: 16px;
}

.profile-pricing.grade-page .check-icon.grade-check {
  background: #2c0846;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-pricing.grade-page .check-icon.grade-check img {
  width: 12px;
  height: 12px;
}

/* Grade Hero Background - Figma colors */
.profile-hero.grade-page .profile-hero-background::before {
  background: linear-gradient(90deg, #e0f7ff 0%, rgba(65, 233, 226, 0.3) 100%);
  opacity: 0.6;
}

/* Grade Features Background - Figma colors */
.profile-features-grid.grade-page .features-gradient-blur {
  background: linear-gradient(267deg, #237cbf 2.44%, #41e9e2 95.58%);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-content > * {
  animation: fadeInUp 0.6s ease-out;
}

.hero-content > *:nth-child(2) {
  animation-delay: 0.2s;
}

.hero-content > *:nth-child(3) {
  animation-delay: 0.4s;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.btn:focus,
.faq-toggle:focus,
.expand-btn:focus {
  outline: 2px solid #ea6666;
  outline-offset: 2px;
}

input:focus,
textarea:focus {
  outline: 2px solid #ea6666;
  outline-offset: 2px;
}
